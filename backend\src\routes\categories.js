const express = require('express');
const db = require('../config/database');

const router = express.Router();

/**
 * GET /api/v1/categories
 * Get all categories with service counts
 */
router.get('/', async (req, res) => {
  try {
    const categories = await db('categories')
      .leftJoin('services', function () {
        this.on('categories.id', '=', 'services.category_id')
          .andOn('services.status', '=', db.raw('?', ['approved']));
      })
      .select(
        'categories.id',
        'categories.name',
        'categories.description',
        'categories.icon',
        'categories.created_at',
        'categories.updated_at'
      )
      .count('services.id as service_count')
      .groupBy('categories.id')
      .orderBy('categories.name');

    res.json({
      success: true,
      data: {
        categories: categories.map(category => ({
          ...category,
          service_count: parseInt(category.service_count) || 0
        }))
      }
    });
  } catch (error) {
    console.error('Get categories error:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'FETCH_CATEGORIES_FAILED',
        message: 'Failed to fetch categories'
      }
    });
  }
});

/**
 * GET /api/v1/categories/:id
 * Get category by ID
 */
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;

    const category = await db('categories')
      .leftJoin('services', function () {
        this.on('categories.id', '=', 'services.category_id')
          .andOn('services.status', '=', db.raw('?', ['approved']));
      })
      .select(
        'categories.id',
        'categories.name',
        'categories.description',
        'categories.icon',
        'categories.created_at',
        'categories.updated_at'
      )
      .count('services.id as service_count')
      .where('categories.id', id)
      .groupBy('categories.id')
      .first();

    if (!category) {
      return res.status(404).json({
        success: false,
        error: {
          code: 'CATEGORY_NOT_FOUND',
          message: 'Category not found'
        }
      });
    }

    res.json({
      success: true,
      data: {
        category: {
          ...category,
          service_count: parseInt(category.service_count) || 0
        }
      }
    });
  } catch (error) {
    console.error('Get category error:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'FETCH_CATEGORY_FAILED',
        message: 'Failed to fetch category'
      }
    });
  }
});

module.exports = router;

