exports.up = function(knex) {
  return knex.schema.createTable('ratings', function(table) {
    table.increments('id').primary();
    table.integer('service_id').unsigned().notNullable();
    table.integer('user_id').unsigned().notNullable();
    table.integer('rating').notNullable().checkBetween([1, 5]);
    table.text('comment');
    table.timestamp('created_at').defaultTo(knex.fn.now());
    table.timestamp('updated_at').defaultTo(knex.fn.now());
    
    // Foreign keys
    table.foreign('service_id').references('id').inTable('services').onDelete('CASCADE');
    table.foreign('user_id').references('id').inTable('users').onDelete('CASCADE');
    
    // Unique constraint - one rating per user per service
    table.unique(['service_id', 'user_id']);
    
    // Indexes
    table.index(['service_id']);
    table.index(['user_id']);
  });
};

exports.down = function(knex) {
  return knex.schema.dropTable('ratings');
};
