exports.up = function (knex) {
  return knex.schema.createTable('ratings', function (table) {
    table.increments('id').primary();
    table.integer('service_id').unsigned().notNullable();
    table.integer('user_id').unsigned().notNullable();
    table.integer('rating').notNullable();
    table.text('comment');
    table.datetime('created_at').notNullable();
    table.datetime('updated_at').notNullable();

    // Foreign keys
    table.foreign('service_id').references('id').inTable('services').onDelete('CASCADE');
    table.foreign('user_id').references('id').inTable('users').onDelete('CASCADE');

    // Unique constraint - one rating per user per service
    table.unique(['service_id', 'user_id']);

    // Indexes
    table.index(['service_id']);
    table.index(['user_id']);

    // Check constraint for rating (1-5) - will be handled in application logic for MySQL 5.6
  });
};

exports.down = function (knex) {
  return knex.schema.dropTable('ratings');
};
