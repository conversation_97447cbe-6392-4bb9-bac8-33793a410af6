const Joi = require('joi');

/**
 * Validation middleware factory
 * @param {Object} schema - Joi validation schema
 * @param {string} property - Request property to validate ('body', 'query', 'params')
 * @returns {Function} Express middleware
 */
function validate(schema, property = 'body') {
  return (req, res, next) => {
    const { error, value } = schema.validate(req[property], {
      abortEarly: false,
      stripUnknown: true
    });

    if (error) {
      const errors = error.details.map(detail => ({
        field: detail.path.join('.'),
        message: detail.message
      }));

      return res.status(400).json({
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: 'Validation failed',
          details: errors
        }
      });
    }

    req[property] = value;
    next();
  };
}

// Common validation schemas
const schemas = {
  // User registration
  register: Joi.object({
    username: Joi.string().alphanum().min(3).max(30).required(),
    email: Joi.string().email().required(),
    password: Joi.string().min(6).required()
  }),

  // User login
  login: Joi.object({
    email: Joi.string().email().required(),
    password: Joi.string().required()
  }),

  // Service creation
  createService: Joi.object({
    name: Joi.string().min(1).max(255).required(),
    description: Joi.string().min(10).required(),
    category_id: Joi.string().required(),
    version: Joi.string().required(),
    tags: Joi.array().items(Joi.string()).optional(),
    repository: Joi.string().uri().optional(),
    documentation: Joi.string().uri().optional(),
    mcp_config: Joi.object().optional()
  }),

  // Service update
  updateService: Joi.object({
    name: Joi.string().min(1).max(255).optional(),
    description: Joi.string().min(10).optional(),
    category_id: Joi.string().optional(),
    version: Joi.string().optional(),
    tags: Joi.array().items(Joi.string()).optional(),
    repository: Joi.string().uri().optional(),
    documentation: Joi.string().uri().optional(),
    mcp_config: Joi.object().optional()
  }),

  // Rating creation
  createRating: Joi.object({
    rating: Joi.number().integer().min(1).max(5).required(),
    comment: Joi.string().max(1000).optional()
  }),

  // Query parameters
  serviceQuery: Joi.object({
    page: Joi.number().integer().min(1).default(1),
    limit: Joi.number().integer().min(1).max(100).default(20),
    category: Joi.string().optional(),
    search: Joi.string().optional(),
    sort: Joi.string().valid('latest', 'popular', 'rating', 'name').default('latest'),
    status: Joi.string().valid('pending', 'approved', 'rejected').optional()
  }),

  // ID parameter
  id: Joi.object({
    id: Joi.number().integer().positive().required()
  })
};

module.exports = {
  validate,
  schemas
};
