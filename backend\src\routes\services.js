const express = require('express');
const db = require('../config/database');
const { authenticate, optionalAuth } = require('../middleware/auth');
const { validate, schemas } = require('../middleware/validation');

const router = express.Router();

/**
 * GET /api/v1/services
 * Get services with filtering, sorting, and pagination
 */
router.get('/', validate(schemas.serviceQuery, 'query'), optionalAuth, async (req, res) => {
  try {
    const { page, limit, category, search, sort, status } = req.query;
    const offset = (page - 1) * limit;

    // Build query
    let query = db('services')
      .join('users', 'services.author_id', 'users.id')
      .join('categories', 'services.category_id', 'categories.id')
      .select(
        'services.*',
        'users.username as author',
        'users.avatar as author_avatar',
        'categories.name as category_name',
        'categories.icon as category_icon'
      );

    // Apply filters
    if (category) {
      query = query.where('services.category_id', category);
    }

    if (search) {
      query = query.where(function() {
        this.where('services.name', 'like', `%${search}%`)
            .orWhere('services.description', 'like', `%${search}%`)
            .orWhere('users.username', 'like', `%${search}%`);
      });
    }

    if (status) {
      query = query.where('services.status', status);
    } else {
      // Default to approved services only
      query = query.where('services.status', 'approved');
    }

    // Apply sorting
    switch (sort) {
      case 'popular':
        query = query.orderBy('services.downloads', 'desc');
        break;
      case 'rating':
        query = query.orderBy('services.rating', 'desc');
        break;
      case 'name':
        query = query.orderBy('services.name', 'asc');
        break;
      case 'latest':
      default:
        query = query.orderBy('services.created_at', 'desc');
        break;
    }

    // Get total count for pagination
    const countQuery = query.clone().clearSelect().clearOrder().count('* as total');
    const [{ total }] = await countQuery;

    // Apply pagination
    const services = await query.limit(limit).offset(offset);

    // Parse JSON fields for MySQL 5.6 compatibility
    const parsedServices = services.map(service => ({
      ...service,
      tags: service.tags ? JSON.parse(service.tags) : [],
      mcp_config: service.mcp_config ? JSON.parse(service.mcp_config) : null
    }));

    res.json({
      success: true,
      data: {
        services: parsedServices,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: parseInt(total),
          pages: Math.ceil(total / limit)
        }
      }
    });
  } catch (error) {
    console.error('Get services error:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'FETCH_SERVICES_FAILED',
        message: 'Failed to fetch services'
      }
    });
  }
});

/**
 * GET /api/v1/services/:id
 * Get service by ID
 */
router.get('/:id', validate(schemas.id, 'params'), optionalAuth, async (req, res) => {
  try {
    const { id } = req.params;

    const service = await db('services')
      .join('users', 'services.author_id', 'users.id')
      .join('categories', 'services.category_id', 'categories.id')
      .select(
        'services.*',
        'users.username as author',
        'users.avatar as author_avatar',
        'users.bio as author_bio',
        'users.website as author_website',
        'categories.name as category_name',
        'categories.icon as category_icon'
      )
      .where('services.id', id)
      .first();

    if (!service) {
      return res.status(404).json({
        success: false,
        error: {
          code: 'SERVICE_NOT_FOUND',
          message: 'Service not found'
        }
      });
    }

    // Parse JSON fields
    service.tags = service.tags ? JSON.parse(service.tags) : [];
    service.mcp_config = service.mcp_config ? JSON.parse(service.mcp_config) : null;

    res.json({
      success: true,
      data: {
        service
      }
    });
  } catch (error) {
    console.error('Get service error:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'FETCH_SERVICE_FAILED',
        message: 'Failed to fetch service'
      }
    });
  }
});

/**
 * POST /api/v1/services
 * Create a new service
 */
router.post('/', authenticate, validate(schemas.createService), async (req, res) => {
  try {
    const serviceData = {
      ...req.body,
      author_id: req.user.id,
      tags: req.body.tags ? JSON.stringify(req.body.tags) : null,
      mcp_config: req.body.mcp_config ? JSON.stringify(req.body.mcp_config) : null,
      status: 'pending'
    };

    const [serviceId] = await db('services').insert(serviceData);

    const service = await db('services')
      .join('users', 'services.author_id', 'users.id')
      .join('categories', 'services.category_id', 'categories.id')
      .select(
        'services.*',
        'users.username as author',
        'categories.name as category_name'
      )
      .where('services.id', serviceId)
      .first();

    // Parse JSON fields
    service.tags = service.tags ? JSON.parse(service.tags) : [];
    service.mcp_config = service.mcp_config ? JSON.parse(service.mcp_config) : null;

    res.status(201).json({
      success: true,
      data: {
        service
      }
    });
  } catch (error) {
    console.error('Create service error:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'CREATE_SERVICE_FAILED',
        message: 'Failed to create service'
      }
    });
  }
});

module.exports = router;
