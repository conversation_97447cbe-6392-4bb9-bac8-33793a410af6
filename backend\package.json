{"name": "mcp-services-backend", "version": "1.0.0", "description": "Backend API for MCP Services Platform", "main": "src/server.js", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js", "test": "jest", "migrate": "knex migrate:latest", "seed": "knex seed:run"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "morgan": "^1.10.0", "dotenv": "^16.3.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "joi": "^17.11.0", "knex": "^3.0.1", "sqlite3": "^5.1.6", "multer": "^1.4.5-lts.1", "express-rate-limit": "^7.1.5", "compression": "^1.7.4"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3"}, "keywords": ["mcp", "api", "express", "nodejs"], "author": "MCP Services Team", "license": "MIT"}