exports.seed = async function(knex) {
  // Deletes ALL existing entries
  await knex('categories').del();
  
  // Inserts seed entries
  await knex('categories').insert([
    {
      id: 'ai',
      name: 'AI 助手',
      description: '人工智能相关的 MCP 服务，包括聊天机器人、自然语言处理、机器学习等',
      icon: '🤖',
      service_count: 0
    },
    {
      id: 'data',
      name: '数据处理',
      description: '数据分析、处理、可视化相关的 MCP 服务',
      icon: '📊',
      service_count: 0
    },
    {
      id: 'web',
      name: '网络服务',
      description: 'Web 开发、API 集成、网络爬虫等相关服务',
      icon: '🌐',
      service_count: 0
    },
    {
      id: 'tool',
      name: '工具类',
      description: '开发工具、实用程序、自动化脚本等',
      icon: '🔧',
      service_count: 0
    },
    {
      id: 'database',
      name: '数据库',
      description: '数据库连接、查询、管理相关的 MCP 服务',
      icon: '🗄️',
      service_count: 0
    },
    {
      id: 'security',
      name: '安全',
      description: '安全检测、加密解密、身份验证等安全相关服务',
      icon: '🔒',
      service_count: 0
    }
  ]);
};
