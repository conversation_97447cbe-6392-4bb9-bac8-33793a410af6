exports.up = function(knex) {
  return knex.schema.createTable('services', function(table) {
    table.increments('id').primary();
    table.string('name', 255).notNullable();
    table.text('description').notNullable();
    table.integer('author_id').unsigned().notNullable();
    table.string('category_id', 50).notNullable();
    table.string('version', 50).notNullable();
    table.integer('downloads').defaultTo(0);
    table.decimal('rating', 3, 2).defaultTo(0);
    table.integer('rating_count').defaultTo(0);
    table.json('tags');
    table.string('icon', 500);
    table.string('repository', 500);
    table.string('documentation', 500);
    table.json('mcp_config');
    table.text('readme');
    table.text('changelog');
    table.enum('status', ['pending', 'approved', 'rejected']).defaultTo('pending');
    table.timestamp('created_at').defaultTo(knex.fn.now());
    table.timestamp('updated_at').defaultTo(knex.fn.now());
    
    // Foreign keys
    table.foreign('author_id').references('id').inTable('users').onDelete('CASCADE');
    table.foreign('category_id').references('id').inTable('categories').onDelete('CASCADE');
    
    // Indexes
    table.index(['author_id']);
    table.index(['category_id']);
    table.index(['status']);
    table.index(['created_at']);
    table.index(['downloads']);
    table.index(['rating']);
  });
};

exports.down = function(knex) {
  return knex.schema.dropTable('services');
};
