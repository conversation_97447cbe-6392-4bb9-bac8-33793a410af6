{"name": "mcp-website", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --fix", "format": "prettier --write src/"}, "dependencies": {"axios": "^1.10.0", "pinia": "3.0.3", "sass": "1.89.2", "vue": "3.5.17", "vue-router": "4.5.1"}, "devDependencies": {"@eslint/js": "9.29.0", "@vitejs/plugin-vue": "6.0.0", "@vitejs/plugin-vue-jsx": "5.0.0", "@vue/eslint-config-prettier": "10.2.0", "eslint": "9.29.0", "eslint-plugin-vue": "10.2.0", "globals": "16.2.0", "prettier": "3.5.3", "vite": "7.0.0", "vite-plugin-vue-devtools": "7.7.7"}}