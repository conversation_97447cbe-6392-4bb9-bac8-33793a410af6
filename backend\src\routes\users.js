const express = require('express');
const db = require('../config/database');
const { authenticate } = require('../middleware/auth');
const { validate, schemas } = require('../middleware/validation');

const router = express.Router();

/**
 * GET /api/v1/users/:id
 * Get user profile by ID
 */
router.get('/:id', validate(schemas.id, 'params'), async (req, res) => {
  try {
    const { id } = req.params;

    const user = await db('users')
      .select('id', 'username', 'email', 'avatar', 'bio', 'website', 'location', 'created_at')
      .where('id', id)
      .first();

    if (!user) {
      return res.status(404).json({
        success: false,
        error: {
          code: 'USER_NOT_FOUND',
          message: 'User not found'
        }
      });
    }

    // Get user's services count
    const servicesCount = await db('services')
      .count('* as count')
      .where('author_id', id)
      .where('status', 'approved')
      .first();

    // Get user's total downloads
    const totalDownloads = await db('services')
      .sum('downloads as total')
      .where('author_id', id)
      .where('status', 'approved')
      .first();

    user.services_count = parseInt(servicesCount.count) || 0;
    user.total_downloads = parseInt(totalDownloads.total) || 0;

    res.json({
      success: true,
      data: {
        user
      }
    });
  } catch (error) {
    console.error('Get user error:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'FETCH_USER_FAILED',
        message: 'Failed to fetch user'
      }
    });
  }
});

/**
 * GET /api/v1/users/:id/services
 * Get services by user ID
 */
router.get('/:id/services', validate(schemas.id, 'params'), async (req, res) => {
  try {
    const { id } = req.params;
    const { page = 1, limit = 20 } = req.query;
    const offset = (page - 1) * limit;

    // Check if user exists
    const user = await db('users').where('id', id).first();
    if (!user) {
      return res.status(404).json({
        success: false,
        error: {
          code: 'USER_NOT_FOUND',
          message: 'User not found'
        }
      });
    }

    // Get user's services
    const services = await db('services')
      .join('categories', 'services.category_id', 'categories.id')
      .select(
        'services.*',
        'categories.name as category_name',
        'categories.icon as category_icon'
      )
      .where('services.author_id', id)
      .where('services.status', 'approved')
      .orderBy('services.created_at', 'desc')
      .limit(limit)
      .offset(offset);

    // Get total count
    const [{ total }] = await db('services')
      .count('* as total')
      .where('author_id', id)
      .where('status', 'approved');

    // Parse JSON fields
    const parsedServices = services.map(service => ({
      ...service,
      tags: service.tags ? JSON.parse(service.tags) : [],
      mcp_config: service.mcp_config ? JSON.parse(service.mcp_config) : null
    }));

    res.json({
      success: true,
      data: {
        services: parsedServices,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: parseInt(total),
          pages: Math.ceil(total / limit)
        }
      }
    });
  } catch (error) {
    console.error('Get user services error:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'FETCH_USER_SERVICES_FAILED',
        message: 'Failed to fetch user services'
      }
    });
  }
});

module.exports = router;
