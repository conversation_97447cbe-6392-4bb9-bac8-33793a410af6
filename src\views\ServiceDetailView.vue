<template>
  <div class="service-detail">
    <!-- Top Navigation -->
    <nav class="top-nav">
      <div class="container">
        <div class="nav-content">
          <div class="nav-left">
            <button class="back-btn" @click="goBack">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <path d="m15 18-6-6 6-6"/>
              </svg>
              返回
            </button>
            <div class="breadcrumb">
              <span class="breadcrumb-item">MCP 服务广场</span>
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <path d="m9 18 6-6-6-6"/>
              </svg>
              <span class="breadcrumb-item current" v-if="service">{{ service.name }}</span>
            </div>
          </div>
          <div class="nav-right">
            <button class="btn btn-secondary" @click="toggleFavorite" v-if="service">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <path d="m19,21l-7-4l-7,4V5a2,2,0,0,1,2-2H17a2,2,0,0,1,2,2Z"/>
              </svg>
              {{ isFavorite ? '取消收藏' : '收藏' }}
            </button>
            <button class="btn btn-primary" @click="useService" v-if="service">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
                <polyline points="7,10 12,15 17,10"/>
                <line x1="12" y1="15" x2="12" y2="3"/>
              </svg>
              使用服务
            </button>
          </div>
        </div>
      </div>
    </nav>

    <!-- Loading State -->
    <div v-if="loading" class="loading-container">
      <div class="spinner"></div>
      <p>加载服务详情中...</p>
    </div>

    <!-- Error State -->
    <div v-else-if="error" class="error-container">
      <div class="error-icon">❌</div>
      <h2>加载失败</h2>
      <p>{{ error }}</p>
      <button class="btn btn-primary" @click="loadService">重试</button>
      <button class="btn btn-secondary" @click="goBack">返回</button>
    </div>

    <!-- Service Detail Content -->
    <div v-else-if="service" class="service-content">
      <!-- Service Header -->
      <header class="service-header">
        <div class="container">
          <div class="service-hero">
            <div class="service-icon-large">
              <img :src="service.icon || '/default-icon.png'" :alt="service.name" />
            </div>
            <div class="service-info">
              <h1 class="service-title">{{ service.name }}</h1>
              <p class="service-author">by {{ service.author }}</p>
              <div class="service-meta">
                <span class="category-tag" :class="service.category">
                  {{ getCategoryName(service.category) }}
                </span>
                <span class="version-tag">v{{ service.version || '1.0.0' }}</span>
                <span class="update-time">{{ getRelativeTime(service.updatedAt) }}</span>
              </div>
              <div class="service-stats-inline">
                <span class="stat">
                  <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                    <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
                    <polyline points="7,10 12,15 17,10"/>
                    <line x1="12" y1="15" x2="12" y2="3"/>
                  </svg>
                  {{ formatNumber(service.downloads || 0) }} 下载
                </span>
                <span class="stat">
                  <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                    <polygon points="12,2 15.09,8.26 22,9.27 17,14.14 18.18,21.02 12,17.77 5.82,21.02 7,14.14 2,9.27 8.91,8.26"/>
                  </svg>
                  {{ service.rating || 0 }} 评分
                </span>
                <span class="stat">
                  <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                    <circle cx="12" cy="12" r="3"/>
                    <path d="M12 1v6m0 6v6m11-7h-6m-6 0H1"/>
                  </svg>
                  {{ service.size || 'N/A' }}
                </span>
              </div>
            </div>
          </div>
        </div>
      </header>

      <!-- Main Content -->
      <main class="service-main">
        <div class="container">
          <div class="content-grid">
            <!-- Left Column -->
            <div class="content-primary">
              <!-- Description -->
              <section class="section">
                <h2>服务描述</h2>
                <p class="description">{{ service.description }}</p>
              </section>

              <!-- Features -->
              <section class="section" v-if="service.features">
                <h2>主要功能</h2>
                <ul class="features-list">
                  <li v-for="feature in service.features" :key="feature">{{ feature }}</li>
                </ul>
              </section>

              <!-- Installation -->
              <section class="section">
                <h2>安装使用</h2>
                <div class="code-block">
                  <pre><code>{{ getInstallCommand() }}</code></pre>
                  <button class="copy-btn" @click="copyToClipboard(getInstallCommand())">
                    复制
                  </button>
                </div>
              </section>

              <!-- Configuration -->
              <section class="section" v-if="service.config">
                <h2>配置示例</h2>
                <div class="code-block">
                  <pre><code>{{ JSON.stringify(service.config, null, 2) }}</code></pre>
                  <button class="copy-btn" @click="copyToClipboard(JSON.stringify(service.config, null, 2))">
                    复制
                  </button>
                </div>
              </section>
            </div>

            <!-- Right Column -->
            <div class="content-sidebar">
              <!-- Stats -->
              <div class="stats-card">
                <h3>统计信息</h3>
                <div class="stats-grid">
                  <div class="stat-item">
                    <span class="stat-icon">👥</span>
                    <span class="stat-value">{{ service.downloads || 0 }}</span>
                    <span class="stat-label">下载量</span>
                  </div>
                  <div class="stat-item">
                    <span class="stat-icon">⭐</span>
                    <span class="stat-value">{{ service.rating || 0 }}</span>
                    <span class="stat-label">评分</span>
                  </div>
                  <div class="stat-item">
                    <span class="stat-icon">📅</span>
                    <span class="stat-value">{{ formatDate(service.updatedAt) }}</span>
                    <span class="stat-label">更新时间</span>
                  </div>
                  <div class="stat-item">
                    <span class="stat-icon">📦</span>
                    <span class="stat-value">{{ service.size || 'N/A' }}</span>
                    <span class="stat-label">大小</span>
                  </div>
                </div>
              </div>

              <!-- Requirements -->
              <div class="requirements-card" v-if="service.requirements">
                <h3>系统要求</h3>
                <ul class="requirements-list">
                  <li v-for="req in service.requirements" :key="req">{{ req }}</li>
                </ul>
              </div>

              <!-- Tags -->
              <div class="tags-card" v-if="service.tags">
                <h3>标签</h3>
                <div class="tags-list">
                  <span v-for="tag in service.tags" :key="tag" class="tag">{{ tag }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>

    <!-- Service Usage Modal -->
    <div v-if="showUsageModal" class="modal-overlay" @click="closeUsageModal">
      <div class="modal-content" @click.stop>
        <div class="modal-header">
          <h3>使用 {{ service?.name }}</h3>
          <button class="modal-close" @click="closeUsageModal">×</button>
        </div>
        <div class="modal-body">
          <p>选择使用方式：</p>
          <div class="usage-options">
            <button class="usage-option" @click="downloadService">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
                <polyline points="7,10 12,15 17,10"/>
                <line x1="12" y1="15" x2="12" y2="3"/>
              </svg>
              <span>下载到本地</span>
            </button>
            <button class="usage-option" @click="copyInstallCommand">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <rect width="14" height="14" x="8" y="8" rx="2" ry="2"/>
                <path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"/>
              </svg>
              <span>复制安装命令</span>
            </button>
            <button class="usage-option" @click="openInBrowser">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"/>
                <polyline points="15,3 21,3 21,9"/>
                <line x1="10" y1="14" x2="21" y2="3"/>
              </svg>
              <span>在浏览器中打开</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ServiceDetailView',
  data() {
    return {
      service: null,
      loading: true,
      error: null,
      showUsageModal: false,
      isFavorite: false
    }
  },
  async mounted() {
    await this.loadService()
    this.checkFavoriteStatus()
  },
  methods: {
    async loadService() {
      this.loading = true
      this.error = null

      try {
        const serviceId = this.$route.params.id

        // Mock API call - replace with actual API
        await new Promise(resolve => setTimeout(resolve, 1000))

        // Mock service data - replace with actual API call
        const mockServices = {
          '1': {
            id: 1,
            name: 'AI 代码助手',
            author: 'OpenAI',
            description: '智能代码生成和优化工具，支持多种编程语言。这个工具可以帮助开发者快速生成高质量的代码，提供智能补全、代码重构建议，以及自动化测试生成等功能。',
            category: 'ai',
            downloads: 1250,
            rating: 4.8,
            updatedAt: new Date('2024-01-15'),
            icon: '/api/placeholder/64/64',
            version: '2.1.0',
            size: '15.2 MB',
            features: [
              '支持 Python, JavaScript, Java, C++ 等多种语言',
              '智能代码补全和建议',
              '自动代码重构和优化',
              '单元测试自动生成',
              '代码质量分析和改进建议'
            ],
            config: {
              "server": "mcp://ai-code-assistant",
              "apiKey": "your-api-key",
              "language": "auto",
              "features": ["completion", "refactor", "test-gen"]
            },
            requirements: [
              'Node.js 16.0 或更高版本',
              '至少 2GB 可用内存',
              '网络连接（用于 AI 模型访问）'
            ],
            tags: ['AI', '代码生成', '开发工具', 'IDE插件']
          },
          '2': {
            id: 2,
            name: '数据分析器',
            author: 'DataCorp',
            description: '强大的数据处理和可视化工具，支持多种数据格式的导入、清洗、分析和可视化。提供丰富的统计分析功能和交互式图表生成。',
            category: 'data',
            downloads: 890,
            rating: 4.6,
            updatedAt: new Date('2024-01-10'),
            icon: '/api/placeholder/64/64',
            version: '1.5.3',
            size: '28.7 MB',
            features: [
              '支持 CSV, JSON, Excel, SQL 数据源',
              '数据清洗和预处理',
              '统计分析和机器学习',
              '交互式图表和仪表板',
              '数据导出和报告生成'
            ],
            config: {
              "server": "mcp://data-analyzer",
              "dataPath": "./data",
              "outputFormat": "json",
              "visualization": true
            },
            requirements: [
              'Python 3.8 或更高版本',
              '至少 4GB 可用内存',
              'pandas, numpy, matplotlib 依赖'
            ],
            tags: ['数据分析', '可视化', '统计', '机器学习']
          },
          '3': {
            id: 3,
            name: 'Web 爬虫',
            author: 'WebTools',
            description: '高效的网页数据抓取工具，支持动态网页、反爬虫机制绕过、数据清洗和结构化输出。适用于电商、新闻、社交媒体等各类网站。',
            category: 'web',
            downloads: 2100,
            rating: 4.9,
            updatedAt: new Date('2024-01-20'),
            icon: '/api/placeholder/64/64',
            version: '3.2.1',
            size: '12.4 MB',
            features: [
              '支持动态网页和 SPA 应用',
              '智能反爬虫机制绕过',
              '分布式爬取和负载均衡',
              '数据清洗和格式化',
              '定时任务和监控'
            ],
            config: {
              "server": "mcp://web-scraper",
              "userAgent": "Mozilla/5.0...",
              "delay": 1000,
              "concurrent": 5
            },
            requirements: [
              'Node.js 14.0 或更高版本',
              '至少 1GB 可用内存',
              '稳定的网络连接'
            ],
            tags: ['爬虫', '数据采集', '自动化', 'Web']
          },
          '4': {
            id: 4,
            name: '文档生成器',
            author: 'DocGen',
            description: '自动生成项目文档和API文档的工具，支持多种编程语言和框架。可以从代码注释、类型定义和配置文件自动生成美观的文档。',
            category: 'tool',
            downloads: 567,
            rating: 4.4,
            updatedAt: new Date('2024-01-08'),
            icon: '/api/placeholder/64/64',
            version: '1.8.0',
            size: '8.9 MB',
            features: [
              '支持多种编程语言',
              '自动API文档生成',
              '代码注释解析',
              '自定义模板和主题',
              '集成CI/CD流程'
            ],
            config: {
              "server": "mcp://doc-generator",
              "inputPath": "./src",
              "outputPath": "./docs",
              "template": "default"
            },
            requirements: [
              'Node.js 12.0 或更高版本',
              '至少 512MB 可用内存',
              '支持的项目结构'
            ],
            tags: ['文档', '自动化', '开发工具', 'API']
          }
        }

        this.service = mockServices[serviceId]

        if (!this.service) {
          throw new Error('服务不存在')
        }

      } catch (error) {
        this.error = error.message || '加载服务详情失败'
      } finally {
        this.loading = false
      }
    },

    getCategoryName(category) {
      const categories = {
        ai: 'AI 助手',
        data: '数据处理',
        web: '网络服务',
        tool: '工具类'
      }
      return categories[category] || category
    },

    formatDate(date) {
      return new Date(date).toLocaleDateString('zh-CN')
    },

    formatNumber(num) {
      if (num >= 1000000) {
        return (num / 1000000).toFixed(1) + 'M'
      }
      if (num >= 1000) {
        return (num / 1000).toFixed(1) + 'K'
      }
      return num.toString()
    },

    getRelativeTime(date) {
      const now = new Date()
      const diffTime = Math.abs(now - new Date(date))
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))

      if (diffDays === 1) return '1天前'
      if (diffDays < 7) return `${diffDays}天前`
      if (diffDays < 30) return `${Math.ceil(diffDays / 7)}周前`
      if (diffDays < 365) return `${Math.ceil(diffDays / 30)}月前`
      return `${Math.ceil(diffDays / 365)}年前`
    },

    getInstallCommand() {
      if (!this.service) return ''
      return `# 安装 ${this.service.name}\nnpm install @mcp/${this.service.name.toLowerCase().replace(/\s+/g, '-')}\n\n# 或使用 yarn\nyarn add @mcp/${this.service.name.toLowerCase().replace(/\s+/g, '-')}`
    },

    async copyToClipboard(text) {
      try {
        await navigator.clipboard.writeText(text)
        // You could add a toast notification here
        console.log('已复制到剪贴板')
      } catch (error) {
        console.error('复制失败:', error)
      }
    },

    useService() {
      this.showUsageModal = true
    },

    closeUsageModal() {
      this.showUsageModal = false
    },

    async downloadService() {
      // Implement download logic
      console.log('下载服务:', this.service.name)
      this.closeUsageModal()

      // Mock download
      const link = document.createElement('a')
      link.href = `#download-${this.service.id}`
      link.download = `${this.service.name}.mcp`
      link.click()
    },

    async copyInstallCommand() {
      const command = this.getInstallCommand()
      await this.copyToClipboard(command)
      this.closeUsageModal()
    },

    openInBrowser() {
      // Open service repository or documentation
      const url = `https://github.com/mcp-services/${this.service.name.toLowerCase().replace(/\s+/g, '-')}`
      window.open(url, '_blank')
      this.closeUsageModal()
    },

    checkFavoriteStatus() {
      // Check if service is in favorites
      const favorites = JSON.parse(localStorage.getItem('mcp-favorites') || '[]')
      this.isFavorite = favorites.includes(this.service?.id)
    },

    toggleFavorite() {
      const favorites = JSON.parse(localStorage.getItem('mcp-favorites') || '[]')

      if (this.isFavorite) {
        const index = favorites.indexOf(this.service.id)
        if (index > -1) {
          favorites.splice(index, 1)
        }
      } else {
        favorites.push(this.service.id)
      }

      localStorage.setItem('mcp-favorites', JSON.stringify(favorites))
      this.isFavorite = !this.isFavorite
    },

    goBack() {
      this.$router.go(-1)
    }
  }
}
</script>

<style lang="scss" scoped>
.service-detail {
  min-height: 100vh;
  background: #fafbfc;
}

.container {
  width: 100%;
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 24px;
}

/* Top Navigation */
.top-nav {
  background: white;
  border-bottom: 1px solid #e1e4e8;
  padding: 16px 0;
  position: sticky;
  top: 0;
  z-index: 100;
}

.nav-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 24px;
}

.nav-left {
  display: flex;
  align-items: center;
  gap: 16px;
  flex: 1;
}

.back-btn {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  background: none;
  border: none;
  color: #656d76;
  cursor: pointer;
  font-size: 14px;
  padding: 6px 12px;
  border-radius: 6px;
  transition: all 0.2s ease;

  &:hover {
    background: #f6f8fa;
    color: #24292f;
  }
}

.breadcrumb {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #656d76;

  .breadcrumb-item {
    &.current {
      color: #24292f;
      font-weight: 500;
    }
  }

  svg {
    color: #d0d7de;
  }
}

.nav-right {
  display: flex;
  align-items: center;
  gap: 12px;
}

/* Loading and Error States */
.loading-container, .error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  text-align: center;
  color: #656d76;
}

.spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #e1e4e8;
  border-top: 3px solid #4f46e5;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.5;
}

/* Service Header */
.service-header {
  background: white;
  border-bottom: 1px solid #e1e4e8;
  padding: 24px 0;
}

.service-hero {
  display: flex;
  align-items: flex-start;
  gap: 20px;
}

.service-icon-large {
  width: 64px;
  height: 64px;
  border-radius: 8px;
  overflow: hidden;
  flex-shrink: 0;
  background: #f6f8fa;
  display: flex;
  align-items: center;
  justify-content: center;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

.service-info {
  flex: 1;
}

.service-title {
  font-size: 28px;
  font-weight: 600;
  color: #24292f;
  margin: 0 0 6px 0;
  line-height: 1.2;
}

.service-author {
  color: #656d76;
  font-size: 16px;
  margin: 0 0 12px 0;
}

.service-meta {
  display: flex;
  gap: 12px;
  align-items: center;
  margin-bottom: 12px;
  flex-wrap: wrap;
}

.category-tag {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  border: 1px solid;

  &.ai {
    background: #dbeafe;
    color: #1e40af;
    border-color: #bfdbfe;
  }

  &.data {
    background: #dcfce7;
    color: #166534;
    border-color: #bbf7d0;
  }

  &.web {
    background: #fef3c7;
    color: #92400e;
    border-color: #fde68a;
  }

  &.tool {
    background: #e0e7ff;
    color: #3730a3;
    border-color: #c7d2fe;
  }
}

.version-tag {
  font-size: 12px;
  color: #656d76;
  background: #f6f8fa;
  padding: 4px 8px;
  border-radius: 12px;
  border: 1px solid #d0d7de;
}

.update-time {
  font-size: 12px;
  color: #656d76;
}

.service-stats-inline {
  display: flex;
  gap: 16px;
  align-items: center;
  flex-wrap: wrap;
}

.stat {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 13px;
  color: #656d76;

  svg {
    color: #656d76;
  }
}

/* Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 6px 16px;
  border: 1px solid transparent;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  white-space: nowrap;

  &-primary {
    background: #4f46e5;
    color: white;
    border-color: #4f46e5;

    &:hover {
      background: #4338ca;
      border-color: #4338ca;
    }
  }

  &-secondary {
    background: #f6f8fa;
    color: #24292f;
    border-color: #d0d7de;

    &:hover {
      background: #f3f4f6;
      border-color: #d0d7de;
    }
  }

  &-ghost {
    background: transparent;
    color: #656d76;
    border-color: transparent;

    &:hover {
      background: #f3f4f6;
      color: #24292f;
    }
  }
}

/* Main Content */
.service-main {
  padding: 32px 0;
}

.content-grid {
  display: grid;
  grid-template-columns: 1fr 300px;
  gap: 32px;
}

.section {
  background: white;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.section h2 {
  font-size: 20px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 16px 0;
}

.description {
  color: #4b5563;
  line-height: 1.6;
  margin: 0;
}

.features-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.features-list li {
  padding: 8px 0;
  color: #4b5563;
  position: relative;
  padding-left: 24px;
}

.features-list li::before {
  content: '✓';
  position: absolute;
  left: 0;
  color: #10b981;
  font-weight: bold;
}

.code-block {
  position: relative;
  background: #1f2937;
  border-radius: 8px;
  padding: 16px;
  overflow-x: auto;

  pre {
    margin: 0;
    color: #e5e7eb;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 14px;
    line-height: 1.5;
  }
}

.copy-btn {
  position: absolute;
  top: 12px;
  right: 12px;
  background: #374151;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 6px 12px;
  font-size: 12px;
  cursor: pointer;
  transition: background 0.3s ease;

  &:hover {
    background: #4b5563;
  }
}

/* Sidebar */
.content-sidebar > div {
  background: white;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.content-sidebar h3 {
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 16px 0;
}

.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  gap: 4px;
}

.stat-icon {
  font-size: 24px;
}

.stat-value {
  font-weight: 600;
  color: #1f2937;
  font-size: 16px;
}

.stat-label {
  font-size: 12px;
  color: #6b7280;
}

.requirements-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.requirements-list li {
  padding: 6px 0;
  color: #4b5563;
  font-size: 14px;
  border-bottom: 1px solid #f3f4f6;
}

.requirements-list li:last-child {
  border-bottom: none;
}

.tags-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.tag {
  background: #f3f4f6;
  color: #374151;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

/* Modal */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 12px;
  width: 90%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #e5e7eb;
}

.modal-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
}

.modal-close {
  background: none;
  border: none;
  font-size: 24px;
  color: #6b7280;
  cursor: pointer;
  padding: 0;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 6px;
  transition: background 0.3s ease;
}

.modal-close:hover {
  background: #f3f4f6;
}

.modal-body {
  padding: 24px;
}

.usage-options {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-top: 16px;
}

.usage-option {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: left;
}

.usage-option:hover {
  background: #f3f4f6;
  border-color: #4f46e5;
}

.usage-option svg {
  color: #4f46e5;
  flex-shrink: 0;
}

.usage-option span {
  font-weight: 500;
  color: #374151;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .content-grid {
    grid-template-columns: 1fr;
    gap: 24px;
  }

  .service-hero {
    flex-direction: column;
    align-items: flex-start;
  }

  .service-actions {
    flex-direction: row;
    width: 100%;
  }
}

@media (max-width: 768px) {
  .container {
    padding: 0 20px;
  }

  .service-title {
    font-size: 24px;
  }

  .service-hero {
    gap: 16px;
  }

  .service-actions {
    flex-direction: column;
  }

  .stats-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .modal-content {
    margin: 20px;
    width: calc(100% - 40px);
  }
}
</style>
