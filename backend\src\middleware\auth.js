const { verifyToken, extractToken } = require('../utils/jwt');
const db = require('../config/database');

/**
 * Authentication middleware
 * Verifies JWT token and attaches user to request
 */
async function authenticate(req, res, next) {
  try {
    const token = extractToken(req.headers.authorization);
    
    if (!token) {
      return res.status(401).json({
        success: false,
        error: {
          code: 'UNAUTHORIZED',
          message: 'Access token is required'
        }
      });
    }

    const decoded = verifyToken(token);
    
    // Get user from database
    const user = await db('users')
      .select('id', 'username', 'email', 'avatar', 'created_at')
      .where('id', decoded.userId)
      .first();

    if (!user) {
      return res.status(401).json({
        success: false,
        error: {
          code: 'UNAUTHORIZED',
          message: 'User not found'
        }
      });
    }

    req.user = user;
    next();
  } catch (error) {
    return res.status(401).json({
      success: false,
      error: {
        code: 'UNAUTHORIZED',
        message: error.message
      }
    });
  }
}

/**
 * Optional authentication middleware
 * Attaches user to request if token is provided, but doesn't require it
 */
async function optionalAuth(req, res, next) {
  try {
    const token = extractToken(req.headers.authorization);
    
    if (token) {
      const decoded = verifyToken(token);
      const user = await db('users')
        .select('id', 'username', 'email', 'avatar', 'created_at')
        .where('id', decoded.userId)
        .first();
      
      if (user) {
        req.user = user;
      }
    }
    
    next();
  } catch (error) {
    // Ignore authentication errors in optional auth
    next();
  }
}

module.exports = {
  authenticate,
  optionalAuth
};
