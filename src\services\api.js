import axios from 'axios'

// Create axios instance with base configuration
const api = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:3001/api/v1',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('auth_token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// Response interceptor to handle errors
api.interceptors.response.use(
  (response) => {
    return response.data
  },
  (error) => {
    if (error.response?.status === 401) {
      // Clear token and redirect to login
      localStorage.removeItem('auth_token')
      localStorage.removeItem('user')
      window.location.href = '/auth'
    }
    
    const errorMessage = error.response?.data?.error?.message || error.message || 'An error occurred'
    return Promise.reject(new Error(errorMessage))
  }
)

// Auth API
export const authAPI = {
  login: (credentials) => api.post('/auth/login', credentials),
  register: (userData) => api.post('/auth/register', userData),
  getProfile: () => api.get('/auth/me')
}

// Services API
export const servicesAPI = {
  getServices: (params = {}) => api.get('/services', { params }),
  getService: (id) => api.get(`/services/${id}`),
  createService: (serviceData) => api.post('/services', serviceData),
  updateService: (id, serviceData) => api.put(`/services/${id}`, serviceData),
  deleteService: (id) => api.delete(`/services/${id}`),
  downloadService: (id) => api.post(`/services/${id}/download`)
}

// Categories API
export const categoriesAPI = {
  getCategories: () => api.get('/categories'),
  getCategory: (id) => api.get(`/categories/${id}`)
}

// Users API
export const usersAPI = {
  getUser: (id) => api.get(`/users/${id}`),
  getUserServices: (id, params = {}) => api.get(`/users/${id}/services`, { params })
}

// Ratings API
export const ratingsAPI = {
  createRating: (serviceId, ratingData) => api.post(`/services/${serviceId}/ratings`, ratingData),
  updateRating: (serviceId, ratingData) => api.put(`/services/${serviceId}/ratings`, ratingData),
  deleteRating: (serviceId) => api.delete(`/services/${serviceId}/ratings`)
}

export default api
