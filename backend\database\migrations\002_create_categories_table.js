exports.up = function (knex) {
  return knex.schema.createTable('categories', function (table) {
    table.string('id', 50).primary();
    table.string('name', 100).notNullable();
    table.text('description');
    table.string('icon', 500);
    table.integer('service_count').defaultTo(0);
    table.datetime('created_at').notNullable();
    table.datetime('updated_at').notNullable();
  });
};

exports.down = function (knex) {
  return knex.schema.dropTable('categories');
};
