<template>
  <div class="mcp-services">
    <!-- Header Section -->
    <header class="header">
      <div class="container">
        <div class="header-content">
          <h1 class="title">MCP 服务广场</h1>
          <p class="subtitle">发现和使用优质的 Model Context Protocol 服务</p>
          <div class="header-actions">
            <button v-if="!authStore.isLoggedIn" class="btn btn-primary" @click="goToAuth">发布服务</button>
            <button v-else class="btn btn-primary" @click="goToPublish">发布服务</button>
            <button v-if="authStore.isLoggedIn" class="btn btn-secondary" @click="logout">退出登录</button>
            <button class="btn btn-secondary" @click="refreshServices">刷新</button>
          </div>
        </div>
      </div>
    </header>

    <!-- Search and Filter Section -->
    <section class="search-section">
      <div class="container">
        <div class="search-bar">
          <input
            v-model="searchQuery"
            type="text"
            placeholder="搜索 MCP 服务..."
            class="search-input"
            @input="filterServices"
          />
          <button class="search-btn">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <circle cx="11" cy="11" r="8"></circle>
              <path d="m21 21-4.35-4.35"></path>
            </svg>
          </button>
        </div>
        <div class="filters">
          <select v-model="selectedCategory" @change="filterServices" class="filter-select">
            <option value="">所有分类</option>
            <option value="ai">AI 助手</option>
            <option value="data">数据处理</option>
            <option value="web">网络服务</option>
            <option value="tool">工具类</option>
          </select>
          <select v-model="sortBy" @change="sortServices" class="filter-select">
            <option value="latest">最新发布</option>
            <option value="popular">最受欢迎</option>
            <option value="name">名称排序</option>
          </select>
        </div>
      </div>
    </section>

    <!-- Services Grid -->
    <section class="services-section">
      <div class="container">
        <div class="services-grid">
          <div
            v-for="service in filteredServices"
            :key="service.id"
            class="service-card"
            @click="viewService(service)"
          >
            <div class="service-header">
              <div class="service-icon">
                <img :src="service.icon || '/default-icon.png'" :alt="service.name" />
              </div>
              <div class="service-info">
                <h3 class="service-name">{{ service.name }}</h3>
                <p class="service-author">by {{ service.author }}</p>
              </div>
              <div class="service-category">
                <span class="category-tag" :class="service.category">{{ getCategoryName(service.category) }}</span>
              </div>
            </div>
            <div class="service-description">
              <p>{{ service.description }}</p>
            </div>
            <div class="service-stats">
              <div class="stat">
                <span class="stat-icon">👥</span>
                <span class="stat-value">{{ service.downloads || 0 }}</span>
                <span class="stat-label">下载</span>
              </div>
              <div class="stat">
                <span class="stat-icon">⭐</span>
                <span class="stat-value">{{ service.rating || 0 }}</span>
                <span class="stat-label">评分</span>
              </div>
              <div class="stat">
                <span class="stat-icon">📅</span>
                <span class="stat-value">{{ formatDate(service.updatedAt) }}</span>
                <span class="stat-label">更新</span>
              </div>
            </div>
            <div class="service-actions">
              <button class="btn btn-primary btn-sm" @click.stop="useService(service)">
                使用服务
              </button>
              <button class="btn btn-secondary btn-sm" @click.stop="viewDetails(service)">
                查看详情
              </button>
            </div>
          </div>
        </div>

        <!-- Loading State -->
        <div v-if="loading" class="loading">
          <div class="spinner"></div>
          <p>加载中...</p>
        </div>

        <!-- Empty State -->
        <div v-if="!loading && filteredServices.length === 0" class="empty-state">
          <div class="empty-icon">📦</div>
          <h3>暂无服务</h3>
          <p>{{ searchQuery ? '没有找到匹配的服务' : '还没有发布的 MCP 服务' }}</p>
        </div>
      </div>
    </section>


  </div>
</template>

<script>
import { useAuthStore } from '@/stores/auth'

export default {
  name: 'HomeView',
  data() {
    return {
      services: [],
      filteredServices: [],
      searchQuery: '',
      selectedCategory: '',
      sortBy: 'latest',
      loading: false,
      authStore: useAuthStore()
    }
  },
  mounted() {
    this.loadServices()
  },
  methods: {
    async loadServices() {
      this.loading = true
      try {
        // Mock data for demonstration
        await new Promise(resolve => setTimeout(resolve, 1000))
        this.services = [
          {
            id: 1,
            name: 'AI 代码助手',
            author: 'OpenAI',
            description: '智能代码生成和优化工具，支持多种编程语言',
            category: 'ai',
            downloads: 1250,
            rating: 4.8,
            updatedAt: new Date('2024-01-15'),
            icon: '/api/placeholder/64/64'
          },
          {
            id: 2,
            name: '数据分析器',
            author: 'DataCorp',
            description: '强大的数据处理和可视化工具',
            category: 'data',
            downloads: 890,
            rating: 4.6,
            updatedAt: new Date('2024-01-10'),
            icon: '/api/placeholder/64/64'
          },
          {
            id: 3,
            name: 'Web 爬虫',
            author: 'WebTools',
            description: '高效的网页数据抓取工具',
            category: 'web',
            downloads: 2100,
            rating: 4.9,
            updatedAt: new Date('2024-01-20'),
            icon: '/api/placeholder/64/64'
          },
          {
            id: 4,
            name: '文档生成器',
            author: 'DocGen',
            description: '自动生成项目文档和API文档',
            category: 'tool',
            downloads: 567,
            rating: 4.4,
            updatedAt: new Date('2024-01-08'),
            icon: '/api/placeholder/64/64'
          }
        ]
        this.filteredServices = [...this.services]
      } catch (error) {
        console.error('Failed to load services:', error)
      } finally {
        this.loading = false
      }
    },
    filterServices() {
      let filtered = [...this.services]

      if (this.searchQuery) {
        filtered = filtered.filter(service =>
          service.name.toLowerCase().includes(this.searchQuery.toLowerCase()) ||
          service.description.toLowerCase().includes(this.searchQuery.toLowerCase()) ||
          service.author.toLowerCase().includes(this.searchQuery.toLowerCase())
        )
      }

      if (this.selectedCategory) {
        filtered = filtered.filter(service => service.category === this.selectedCategory)
      }

      this.filteredServices = filtered
      this.sortServices()
    },
    sortServices() {
      switch (this.sortBy) {
        case 'latest':
          this.filteredServices.sort((a, b) => new Date(b.updatedAt) - new Date(a.updatedAt))
          break
        case 'popular':
          this.filteredServices.sort((a, b) => b.downloads - a.downloads)
          break
        case 'name':
          this.filteredServices.sort((a, b) => a.name.localeCompare(b.name))
          break
      }
    },
    getCategoryName(category) {
      const categories = {
        ai: 'AI 助手',
        data: '数据处理',
        web: '网络服务',
        tool: '工具类'
      }
      return categories[category] || category
    },
    formatDate(date) {
      return new Date(date).toLocaleDateString('zh-CN')
    },
    refreshServices() {
      this.loadServices()
    },
    viewService(service) {
      console.log('Viewing service:', service)
      // TODO: Navigate to service detail page
    },
    useService(service) {
      console.log('Using service:', service)
      // TODO: Implement service usage logic
    },
    viewDetails(service) {
      console.log('Viewing details:', service)
      // TODO: Show service details modal or navigate to detail page
    },
    goToAuth() {
      this.$router.push('/auth')
    },
    goToPublish() {
      this.$router.push('/publish')
    },
    async logout() {
      await this.authStore.logout()
      // Optionally show a message or refresh the page
    }
  }
}
</script>

<style scoped>
.mcp-services {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

/* Header Styles */
.header {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  padding: 60px 0;
  text-align: center;
  color: white;
}

.title {
  font-size: 3rem;
  font-weight: 700;
  margin-bottom: 1rem;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.subtitle {
  font-size: 1.2rem;
  margin-bottom: 2rem;
  opacity: 0.9;
}

.header-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

/* Button Styles */
.btn {
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

.btn-primary {
  background: #4f46e5;
  color: white;
}

.btn-primary:hover {
  background: #4338ca;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(79, 70, 229, 0.4);
}

.btn-secondary {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.btn-secondary:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
}

.btn-sm {
  padding: 8px 16px;
  font-size: 0.9rem;
}

/* Search Section */
.search-section {
  background: white;
  padding: 2rem 0;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.search-bar {
  display: flex;
  max-width: 600px;
  margin: 0 auto 1.5rem;
  position: relative;
}

.search-input {
  flex: 1;
  padding: 16px 20px;
  border: 2px solid #e5e7eb;
  border-radius: 12px 0 0 12px;
  font-size: 1rem;
  outline: none;
  transition: border-color 0.3s ease;
}

.search-input:focus {
  border-color: #4f46e5;
}

.search-btn {
  padding: 16px 20px;
  background: #4f46e5;
  color: white;
  border: none;
  border-radius: 0 12px 12px 0;
  cursor: pointer;
  transition: background 0.3s ease;
}

.search-btn:hover {
  background: #4338ca;
}

.filters {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

.filter-select {
  padding: 12px 16px;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  font-size: 1rem;
  background: white;
  cursor: pointer;
  outline: none;
  transition: border-color 0.3s ease;
}

.filter-select:focus {
  border-color: #4f46e5;
}

/* Services Section */
.services-section {
  padding: 3rem 0;
}

.services-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 2rem;
  margin-bottom: 2rem;
}

.service-card {
  background: white;
  border-radius: 16px;
  padding: 1.5rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  cursor: pointer;
  border: 1px solid #f3f4f6;
}

.service-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

.service-header {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  margin-bottom: 1rem;
}

.service-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  overflow: hidden;
  flex-shrink: 0;
}

.service-icon img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.service-info {
  flex: 1;
}

.service-name {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0 0 0.25rem 0;
  color: #1f2937;
}

.service-author {
  color: #6b7280;
  margin: 0;
  font-size: 0.9rem;
}

.service-category {
  flex-shrink: 0;
}

.category-tag {
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
  text-transform: uppercase;
}

.category-tag.ai {
  background: #dbeafe;
  color: #1e40af;
}

.category-tag.data {
  background: #d1fae5;
  color: #065f46;
}

.category-tag.web {
  background: #fef3c7;
  color: #92400e;
}

.category-tag.tool {
  background: #e0e7ff;
  color: #5b21b6;
}

.service-description {
  margin-bottom: 1.5rem;
}

.service-description p {
  color: #4b5563;
  line-height: 1.6;
  margin: 0;
}

.service-stats {
  display: flex;
  gap: 1.5rem;
  margin-bottom: 1.5rem;
  padding: 1rem;
  background: #f9fafb;
  border-radius: 8px;
}

.stat {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.25rem;
}

.stat-icon {
  font-size: 1.2rem;
}

.stat-value {
  font-weight: 600;
  color: #1f2937;
}

.stat-label {
  font-size: 0.8rem;
  color: #6b7280;
}

.service-actions {
  display: flex;
  gap: 0.75rem;
}

/* Loading and Empty States */
.loading {
  text-align: center;
  padding: 3rem;
  color: white;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-top: 4px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.empty-state {
  text-align: center;
  padding: 3rem;
  color: white;
}

.empty-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
}

.empty-state h3 {
  margin-bottom: 0.5rem;
}



/* Responsive Design */
@media (max-width: 768px) {
  .title {
    font-size: 2rem;
  }

  .services-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .service-stats {
    gap: 1rem;
  }

  .filters {
    flex-direction: column;
    align-items: center;
  }

  .filter-select {
    width: 100%;
    max-width: 300px;
  }

  .search-bar {
    margin: 0 auto 1rem;
  }

  .header-actions {
    flex-direction: column;
    align-items: center;
  }

  .service-actions {
    flex-direction: column;
  }
}

@media (min-width: 1920px) {
  .container {
    max-width: 1400px;
  }

  .services-grid {
    grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  }
}
</style>
