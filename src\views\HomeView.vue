<template>
  <div class="mcp-services">
    <!-- Top Navigation -->
    <nav class="top-nav">
      <div class="container">
        <div class="nav-content">
          <div class="nav-left">
            <div class="logo">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <path d="M12 2L2 7l10 5 10-5-10-5z"/>
                <path d="M2 17l10 5 10-5"/>
                <path d="M2 12l10 5 10-5"/>
              </svg>
              <span>MCP 服务广场</span>
            </div>
            <div class="nav-search">
              <input
                v-model="searchQuery"
                type="text"
                placeholder="搜索服务、作者或关键词..."
                class="search-input"
                @input="filterServices"
              />
              <svg class="search-icon" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <circle cx="11" cy="11" r="8"></circle>
                <path d="m21 21-4.35-4.35"></path>
              </svg>
            </div>
          </div>
          <div class="nav-right">
            <button v-if="!authStore.isLoggedIn" class="btn btn-primary" @click="goToAuth">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <path d="M12 5v14m-7-7h14"/>
              </svg>
              发布服务
            </button>
            <button v-else class="btn btn-primary" @click="goToPublish">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <path d="M12 5v14m-7-7h14"/>
              </svg>
              发布服务
            </button>
            <button class="btn btn-secondary" @click="refreshServices">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <path d="M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8"/>
                <path d="M21 3v5h-5"/>
                <path d="M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16"/>
                <path d="M3 21v-5h5"/>
              </svg>
              刷新
            </button>
            <button v-if="authStore.isLoggedIn" class="btn btn-ghost" @click="logout">退出登录</button>
          </div>
        </div>
      </div>
    </nav>

    <!-- Main Content -->
    <div class="main-content">
      <div class="container">
        <div class="content-layout">
          <!-- Sidebar -->
          <aside class="sidebar">
            <div class="sidebar-section">
              <h3>分类筛选</h3>
              <div class="category-filters">
                <label class="filter-item" :class="{ active: selectedCategory === '' }">
                  <input type="radio" v-model="selectedCategory" value="" @change="filterServices" />
                  <span class="filter-text">
                    <span class="filter-icon">📦</span>
                    全部服务
                  </span>
                  <span class="filter-count">{{ services.length }}</span>
                </label>
                <label class="filter-item" :class="{ active: selectedCategory === 'ai' }">
                  <input type="radio" v-model="selectedCategory" value="ai" @change="filterServices" />
                  <span class="filter-text">
                    <span class="filter-icon">🤖</span>
                    AI 助手
                  </span>
                  <span class="filter-count">{{ getCategoryCount('ai') }}</span>
                </label>
                <label class="filter-item" :class="{ active: selectedCategory === 'data' }">
                  <input type="radio" v-model="selectedCategory" value="data" @change="filterServices" />
                  <span class="filter-text">
                    <span class="filter-icon">📊</span>
                    数据处理
                  </span>
                  <span class="filter-count">{{ getCategoryCount('data') }}</span>
                </label>
                <label class="filter-item" :class="{ active: selectedCategory === 'web' }">
                  <input type="radio" v-model="selectedCategory" value="web" @change="filterServices" />
                  <span class="filter-text">
                    <span class="filter-icon">🌐</span>
                    网络服务
                  </span>
                  <span class="filter-count">{{ getCategoryCount('web') }}</span>
                </label>
                <label class="filter-item" :class="{ active: selectedCategory === 'tool' }">
                  <input type="radio" v-model="selectedCategory" value="tool" @change="filterServices" />
                  <span class="filter-text">
                    <span class="filter-icon">🔧</span>
                    工具类
                  </span>
                  <span class="filter-count">{{ getCategoryCount('tool') }}</span>
                </label>
              </div>
            </div>

            <div class="sidebar-section">
              <h3>排序方式</h3>
              <select v-model="sortBy" @change="sortServices" class="sort-select">
                <option value="latest">最新发布</option>
                <option value="popular">最受欢迎</option>
                <option value="rating">评分最高</option>
                <option value="name">名称排序</option>
              </select>
            </div>

            <div class="sidebar-section">
              <h3>统计信息</h3>
              <div class="stats-overview">
                <div class="stat-item">
                  <span class="stat-number">{{ services.length }}</span>
                  <span class="stat-label">总服务数</span>
                </div>
                <div class="stat-item">
                  <span class="stat-number">{{ getTotalDownloads() }}</span>
                  <span class="stat-label">总下载量</span>
                </div>
                <div class="stat-item">
                  <span class="stat-number">{{ getAverageRating() }}</span>
                  <span class="stat-label">平均评分</span>
                </div>
              </div>
            </div>
          </aside>

          <!-- Services List -->
          <main class="services-main">
            <div class="services-header">
              <div class="results-info">
                <h2>{{ getResultsTitle() }}</h2>
                <p>{{ filteredServices.length }} 个服务</p>
              </div>
            </div>

            <!-- Services Grid -->
            <div class="services-grid">
              <div
                v-for="service in filteredServices"
                :key="service.id"
                class="service-card"
                @click="viewService(service)"
              >
                <div class="service-header">
                  <div class="service-icon">
                    <img :src="service.icon || '/default-icon.png'" :alt="service.name" />
                  </div>
                  <div class="service-info">
                    <h3 class="service-name">{{ service.name }}</h3>
                    <p class="service-author">{{ service.author }}</p>
                    <div class="service-meta">
                      <span class="category-tag" :class="service.category">{{ getCategoryName(service.category) }}</span>
                      <span class="version-tag">v{{ service.version || '1.0.0' }}</span>
                      <span class="update-time">{{ getRelativeTime(service.updatedAt) }}</span>
                    </div>
                  </div>
                  <div class="service-actions">
                    <button class="btn btn-sm btn-primary" @click.stop="useService(service)">
                      <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                        <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
                        <polyline points="7,10 12,15 17,10"/>
                        <line x1="12" y1="15" x2="12" y2="3"/>
                      </svg>
                      使用
                    </button>
                    <button class="btn btn-sm btn-ghost" @click.stop="viewDetails(service)">详情</button>
                  </div>
                </div>
                <div class="service-description">
                  <p>{{ service.description }}</p>
                </div>
                <div class="service-footer">
                  <div class="service-stats">
                    <span class="stat">
                      <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                        <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
                        <polyline points="7,10 12,15 17,10"/>
                        <line x1="12" y1="15" x2="12" y2="3"/>
                      </svg>
                      {{ formatNumber(service.downloads || 0) }}
                    </span>
                    <span class="stat">
                      <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                        <polygon points="12,2 15.09,8.26 22,9.27 17,14.14 18.18,21.02 12,17.77 5.82,21.02 7,14.14 2,9.27 8.91,8.26"/>
                      </svg>
                      {{ service.rating || 0 }}
                    </span>
                    <span class="stat">
                      <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                        <circle cx="12" cy="12" r="3"/>
                        <path d="M12 1v6m0 6v6m11-7h-6m-6 0H1"/>
                      </svg>
                      {{ service.size || 'N/A' }}
                    </span>
                  </div>
                  <div class="service-tags" v-if="service.tags">
                    <span v-for="tag in service.tags?.slice(0, 3)" :key="tag" class="tag">{{ tag }}</span>
                  </div>
                </div>
              </div>
            </div>

            <!-- Loading State -->
            <div v-if="loading" class="loading">
              <div class="spinner"></div>
              <p>加载中...</p>
            </div>

            <!-- Empty State -->
            <div v-if="!loading && filteredServices.length === 0" class="empty-state">
              <div class="empty-icon">📦</div>
              <h3>暂无服务</h3>
              <p>{{ searchQuery ? '没有找到匹配的服务' : '还没有发布的 MCP 服务' }}</p>
            </div>
          </main>
        </div>
      </div>
    </div>

    <!-- Service Usage Modal -->
    <div v-if="showUsageModal && selectedService" class="modal-overlay" @click="closeUsageModal">
      <div class="modal-content" @click.stop>
        <div class="modal-header">
          <h3>使用 {{ selectedService.name }}</h3>
          <button class="modal-close" @click="closeUsageModal">×</button>
        </div>
        <div class="modal-body">
          <p>选择使用方式：</p>
          <div class="usage-options">
            <button class="usage-option" @click="downloadService">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
                <polyline points="7,10 12,15 17,10"/>
                <line x1="12" y1="15" x2="12" y2="3"/>
              </svg>
              <span>下载到本地</span>
            </button>
            <button class="usage-option" @click="copyInstallCommand">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <rect width="14" height="14" x="8" y="8" rx="2" ry="2"/>
                <path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"/>
              </svg>
              <span>复制安装命令</span>
            </button>
            <button class="usage-option" @click="openServiceInBrowser">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"/>
                <polyline points="15,3 21,3 21,9"/>
                <line x1="10" y1="14" x2="21" y2="3"/>
              </svg>
              <span>在浏览器中打开</span>
            </button>
            <button class="usage-option" @click="viewServiceDetails">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <path d="M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z"/>
                <path d="M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z"/>
              </svg>
              <span>查看详细信息</span>
            </button>
          </div>
        </div>
      </div>
    </div>

  </div>
</template>

<script>
import { useAuthStore } from '@/stores/auth'

export default {
  name: 'HomeView',
  data() {
    return {
      services: [],
      filteredServices: [],
      searchQuery: '',
      selectedCategory: '',
      sortBy: 'latest',
      loading: false,
      authStore: useAuthStore(),
      showUsageModal: false,
      selectedService: null
    }
  },
  mounted() {
    this.loadServices()
  },
  methods: {
    async loadServices() {
      this.loading = true
      try {
        // Mock data for demonstration
        await new Promise(resolve => setTimeout(resolve, 1000))
        this.services = [
          {
            id: 1,
            name: 'AI 代码助手',
            author: 'OpenAI',
            description: '智能代码生成和优化工具，支持多种编程语言。提供智能补全、代码重构建议，以及自动化测试生成等功能。',
            category: 'ai',
            downloads: 1250,
            rating: 4.8,
            updatedAt: new Date('2024-01-15'),
            icon: '/api/placeholder/64/64',
            version: '2.1.0',
            size: '15.2 MB',
            tags: ['AI', '代码生成', '开发工具', 'IDE插件']
          },
          {
            id: 2,
            name: '数据分析器',
            author: 'DataCorp',
            description: '强大的数据处理和可视化工具，支持多种数据格式的导入、清洗、分析和可视化。',
            category: 'data',
            downloads: 890,
            rating: 4.6,
            updatedAt: new Date('2024-01-10'),
            icon: '/api/placeholder/64/64',
            version: '1.5.3',
            size: '28.7 MB',
            tags: ['数据分析', '可视化', '统计', '机器学习']
          },
          {
            id: 3,
            name: 'Web 爬虫',
            author: 'WebTools',
            description: '高效的网页数据抓取工具，支持动态网页、反爬虫机制绕过、数据清洗和结构化输出。',
            category: 'web',
            downloads: 2100,
            rating: 4.9,
            updatedAt: new Date('2024-01-20'),
            icon: '/api/placeholder/64/64',
            version: '3.2.1',
            size: '12.4 MB',
            tags: ['爬虫', '数据采集', '自动化', 'Web']
          },
          {
            id: 4,
            name: '文档生成器',
            author: 'DocGen',
            description: '自动生成项目文档和API文档的工具，支持多种编程语言和框架。',
            category: 'tool',
            downloads: 567,
            rating: 4.4,
            updatedAt: new Date('2024-01-08'),
            icon: '/api/placeholder/64/64',
            version: '1.8.0',
            size: '8.9 MB',
            tags: ['文档', '自动化', '开发工具', 'API']
          },
          {
            id: 5,
            name: '图像处理器',
            author: 'ImageTech',
            description: '专业的图像处理和编辑工具，支持批量处理、格式转换、滤镜应用等功能。',
            category: 'tool',
            downloads: 1580,
            rating: 4.7,
            updatedAt: new Date('2024-01-18'),
            icon: '/api/placeholder/64/64',
            version: '2.3.0',
            size: '45.1 MB',
            tags: ['图像处理', '批量操作', '格式转换']
          },
          {
            id: 6,
            name: '智能翻译',
            author: 'TranslateAI',
            description: '基于AI的多语言翻译服务，支持100+语言，提供高质量的翻译结果。',
            category: 'ai',
            downloads: 3200,
            rating: 4.9,
            updatedAt: new Date('2024-01-22'),
            icon: '/api/placeholder/64/64',
            version: '1.2.5',
            size: '22.8 MB',
            tags: ['翻译', 'AI', '多语言', '自然语言处理']
          }
        ]
        this.filteredServices = [...this.services]
      } catch (error) {
        console.error('Failed to load services:', error)
      } finally {
        this.loading = false
      }
    },
    filterServices() {
      let filtered = [...this.services]

      if (this.searchQuery) {
        filtered = filtered.filter(service =>
          service.name.toLowerCase().includes(this.searchQuery.toLowerCase()) ||
          service.description.toLowerCase().includes(this.searchQuery.toLowerCase()) ||
          service.author.toLowerCase().includes(this.searchQuery.toLowerCase())
        )
      }

      if (this.selectedCategory) {
        filtered = filtered.filter(service => service.category === this.selectedCategory)
      }

      this.filteredServices = filtered
      this.sortServices()
    },
    sortServices() {
      switch (this.sortBy) {
        case 'latest':
          this.filteredServices.sort((a, b) => new Date(b.updatedAt) - new Date(a.updatedAt))
          break
        case 'popular':
          this.filteredServices.sort((a, b) => b.downloads - a.downloads)
          break
        case 'name':
          this.filteredServices.sort((a, b) => a.name.localeCompare(b.name))
          break
      }
    },
    getCategoryName(category) {
      const categories = {
        ai: 'AI 助手',
        data: '数据处理',
        web: '网络服务',
        tool: '工具类'
      }
      return categories[category] || category
    },

    getCategoryCount(category) {
      return this.services.filter(service => service.category === category).length
    },

    getTotalDownloads() {
      const total = this.services.reduce((sum, service) => sum + (service.downloads || 0), 0)
      return this.formatNumber(total)
    },

    getAverageRating() {
      const ratings = this.services.filter(service => service.rating).map(service => service.rating)
      if (ratings.length === 0) return '0.0'
      const average = ratings.reduce((sum, rating) => sum + rating, 0) / ratings.length
      return average.toFixed(1)
    },

    getResultsTitle() {
      if (this.searchQuery) {
        return `搜索 "${this.searchQuery}" 的结果`
      }
      if (this.selectedCategory) {
        return this.getCategoryName(this.selectedCategory)
      }
      return '所有服务'
    },

    formatNumber(num) {
      if (num >= 1000000) {
        return (num / 1000000).toFixed(1) + 'M'
      }
      if (num >= 1000) {
        return (num / 1000).toFixed(1) + 'K'
      }
      return num.toString()
    },

    getRelativeTime(date) {
      const now = new Date()
      const diffTime = Math.abs(now - new Date(date))
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))

      if (diffDays === 1) return '1天前'
      if (diffDays < 7) return `${diffDays}天前`
      if (diffDays < 30) return `${Math.ceil(diffDays / 7)}周前`
      if (diffDays < 365) return `${Math.ceil(diffDays / 30)}月前`
      return `${Math.ceil(diffDays / 365)}年前`
    },

    formatDate(date) {
      return new Date(date).toLocaleDateString('zh-CN')
    },
    refreshServices() {
      this.loadServices()
    },
    viewService(service) {
      // Navigate to service detail page
      this.$router.push({ name: 'service-detail', params: { id: service.id } })
    },
    useService(service) {
      this.selectedService = service
      this.showUsageModal = true
    },
    viewDetails(service) {
      // Navigate to service detail page
      this.$router.push({ name: 'service-detail', params: { id: service.id } })
    },
    goToAuth() {
      this.$router.push('/auth')
    },
    goToPublish() {
      this.$router.push('/publish')
    },
    async logout() {
      await this.authStore.logout()
      // Optionally show a message or refresh the page
    },

    closeUsageModal() {
      this.showUsageModal = false
      this.selectedService = null
    },

    async downloadService() {
      if (!this.selectedService) return

      // Mock download functionality
      console.log('下载服务:', this.selectedService.name)

      // Create a mock download
      const link = document.createElement('a')
      link.href = `#download-${this.selectedService.id}`
      link.download = `${this.selectedService.name}.mcp`
      link.click()

      this.closeUsageModal()
    },

    async copyInstallCommand() {
      if (!this.selectedService) return

      const command = `# 安装 ${this.selectedService.name}\nnpm install @mcp/${this.selectedService.name.toLowerCase().replace(/\s+/g, '-')}\n\n# 或使用 yarn\nyarn add @mcp/${this.selectedService.name.toLowerCase().replace(/\s+/g, '-')}`

      try {
        await navigator.clipboard.writeText(command)
        console.log('安装命令已复制到剪贴板')
        // You could add a toast notification here
      } catch (error) {
        console.error('复制失败:', error)
      }

      this.closeUsageModal()
    },

    openServiceInBrowser() {
      if (!this.selectedService) return

      // Open service repository or documentation
      const url = `https://github.com/mcp-services/${this.selectedService.name.toLowerCase().replace(/\s+/g, '-')}`
      window.open(url, '_blank')

      this.closeUsageModal()
    },

    viewServiceDetails() {
      if (!this.selectedService) return

      this.closeUsageModal()
      this.$router.push({ name: 'service-detail', params: { id: this.selectedService.id } })
    }
  }
}
</script>

<style lang="scss" scoped>
.mcp-services {
  min-height: 100vh;
  background: #fafbfc;
}

.container {
  width: 100%;
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 24px;
}

/* Top Navigation */
.top-nav {
  background: white;
  border-bottom: 1px solid #e1e4e8;
  padding: 16px 0;
  position: sticky;
  top: 0;
  z-index: 100;
}

.nav-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 24px;
}

.nav-left {
  display: flex;
  align-items: center;
  gap: 24px;
  flex: 1;
}

.logo {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 18px;
  font-weight: 600;
  color: #24292f;

  svg {
    color: #4f46e5;
  }
}

.nav-search {
  position: relative;
  max-width: 400px;
  flex: 1;

  .search-input {
    width: 100%;
    padding: 8px 12px 8px 36px;
    border: 1px solid #d0d7de;
    border-radius: 6px;
    font-size: 14px;
    background: #f6f8fa;
    transition: all 0.2s ease;

    &:focus {
      outline: none;
      border-color: #4f46e5;
      background: white;
      box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
    }

    &::placeholder {
      color: #656d76;
    }
  }

  .search-icon {
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: #656d76;
  }
}

.nav-right {
  display: flex;
  align-items: center;
  gap: 12px;
}

/* Button Styles */
.btn {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 6px 16px;
  border: 1px solid transparent;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  white-space: nowrap;

  &-primary {
    background: #4f46e5;
    color: white;
    border-color: #4f46e5;

    &:hover {
      background: #4338ca;
      border-color: #4338ca;
    }
  }

  &-secondary {
    background: #f6f8fa;
    color: #24292f;
    border-color: #d0d7de;

    &:hover {
      background: #f3f4f6;
      border-color: #d0d7de;
    }
  }

  &-ghost {
    background: transparent;
    color: #656d76;
    border-color: transparent;

    &:hover {
      background: #f3f4f6;
      color: #24292f;
    }
  }

  &-sm {
    padding: 4px 8px;
    font-size: 12px;
  }
}

/* Main Content Layout */
.main-content {
  padding: 24px 0;
}

.content-layout {
  display: grid;
  grid-template-columns: 280px 1fr;
  gap: 32px;
  align-items: start;
}

/* Sidebar */
.sidebar {
  background: white;
  border: 1px solid #e1e4e8;
  border-radius: 8px;
  padding: 24px;
  position: sticky;
  top: 100px;
  height: fit-content;
}

.sidebar-section {
  margin-bottom: 32px;

  &:last-child {
    margin-bottom: 0;
  }

  h3 {
    font-size: 16px;
    font-weight: 600;
    color: #24292f;
    margin: 0 0 16px 0;
    padding-bottom: 8px;
    border-bottom: 1px solid #e1e4e8;
  }
}

.category-filters {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.filter-item {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background: #f6f8fa;
  }

  &.active {
    background: #dbeafe;
    color: #1e40af;
  }

  input[type="radio"] {
    display: none;
  }

  .filter-text {
    display: flex;
    align-items: center;
    gap: 8px;
    flex: 1;
    font-size: 14px;
  }

  .filter-icon {
    font-size: 16px;
  }

  .filter-count {
    font-size: 12px;
    color: #656d76;
    background: #f6f8fa;
    padding: 2px 6px;
    border-radius: 12px;
    min-width: 20px;
    text-align: center;
  }

  &.active .filter-count {
    background: #bfdbfe;
    color: #1e40af;
  }
}

.sort-select {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #d0d7de;
  border-radius: 6px;
  font-size: 14px;
  background: white;
  cursor: pointer;

  &:focus {
    outline: none;
    border-color: #4f46e5;
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
  }
}

.stats-overview {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 12px;
  background: #f6f8fa;
  border-radius: 6px;
  text-align: center;

  .stat-number {
    font-size: 20px;
    font-weight: 600;
    color: #24292f;
  }

  .stat-label {
    font-size: 12px;
    color: #656d76;
    margin-top: 4px;
  }
}

/* Services Main */
.services-main {
  flex: 1;
}

.services-header {
  margin-bottom: 24px;

  .results-info {
    h2 {
      font-size: 24px;
      font-weight: 600;
      color: #24292f;
      margin: 0 0 4px 0;
    }

    p {
      font-size: 14px;
      color: #656d76;
      margin: 0;
    }
  }
}

.services-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 16px;
}

.service-card {
  background: white;
  border: 1px solid #e1e4e8;
  border-radius: 8px;
  padding: 16px;
  transition: all 0.2s ease;
  cursor: pointer;

  &:hover {
    border-color: #d0d7de;
    box-shadow: 0 3px 12px rgba(0, 0, 0, 0.1);
  }
}

.service-header {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  margin-bottom: 12px;
}

.service-icon {
  width: 40px;
  height: 40px;
  border-radius: 6px;
  overflow: hidden;
  flex-shrink: 0;
  background: #f6f8fa;
  display: flex;
  align-items: center;
  justify-content: center;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

.service-info {
  flex: 1;
  min-width: 0;
}

.service-name {
  font-size: 16px;
  font-weight: 600;
  color: #0969da;
  margin: 0 0 2px 0;
  line-height: 1.3;

  &:hover {
    text-decoration: underline;
  }
}

.service-author {
  font-size: 12px;
  color: #656d76;
  margin: 0 0 6px 0;
}

.service-meta {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.service-actions {
  display: flex;
  gap: 6px;
  margin-left: auto;
}

.category-tag {
  display: inline-block;
  padding: 2px 6px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 500;
  border: 1px solid;

  &.ai {
    background: #dbeafe;
    color: #1e40af;
    border-color: #bfdbfe;
  }

  &.data {
    background: #dcfce7;
    color: #166534;
    border-color: #bbf7d0;
  }

  &.web {
    background: #fef3c7;
    color: #92400e;
    border-color: #fde68a;
  }

  &.tool {
    background: #e0e7ff;
    color: #3730a3;
    border-color: #c7d2fe;
  }
}

.version-tag {
  font-size: 11px;
  color: #656d76;
  background: #f6f8fa;
  padding: 2px 6px;
  border-radius: 12px;
  border: 1px solid #d0d7de;
}

.update-time {
  font-size: 11px;
  color: #656d76;
}

.service-description {
  margin-bottom: 12px;
  height: 42px;
  p {
    color: #656d76;
    line-height: 1.5;
    margin: 0;
    font-size: 14px;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
}

.service-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 12px;
  margin-top: 12px;
  padding-top: 12px;
  border-top: 1px solid #e1e4e8;
}

.service-stats {
  display: flex;
  gap: 16px;
  align-items: center;
}

.stat {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #656d76;

  svg {
    color: #656d76;
  }
}

.service-tags {
  display: flex;
  gap: 4px;
  flex-wrap: wrap;
}

.tag {
  font-size: 11px;
  color: #656d76;
  background: #f6f8fa;
  padding: 2px 6px;
  border-radius: 12px;
  border: 1px solid #d0d7de;
}

/* Loading and Empty States */
.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 64px 32px;
  color: #656d76;

  .spinner {
    width: 32px;
    height: 32px;
    border: 3px solid #e1e4e8;
    border-top: 3px solid #4f46e5;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 16px;
  }

  p {
    font-size: 14px;
    margin: 0;
  }
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 64px 32px;
  text-align: center;
  color: #656d76;

  .empty-icon {
    font-size: 48px;
    margin-bottom: 16px;
    opacity: 0.5;
  }

  h3 {
    font-size: 18px;
    font-weight: 600;
    margin: 0 0 8px 0;
    color: #24292f;
  }

  p {
    font-size: 14px;
    margin: 0;
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 1200px) {
  .content-layout {
    grid-template-columns: 240px 1fr;
    gap: 24px;
  }

  .services-grid {
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  }
}

@media (max-width: 768px) {
  .container {
    padding: 0 16px;
  }

  .nav-content {
    flex-direction: column;
    gap: 16px;
  }

  .nav-left {
    width: 100%;
  }

  .nav-search {
    max-width: none;
  }

  .content-layout {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .sidebar {
    position: static;
    order: 2;
  }

  .services-grid {
    grid-template-columns: 1fr;
  }

  .service-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .service-actions {
    margin-left: 0;
    margin-top: 8px;
  }
}

/* Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 12px;
  width: 90%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #e5e7eb;

  h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: #1f2937;
  }
}

.modal-close {
  background: none;
  border: none;
  font-size: 24px;
  color: #6b7280;
  cursor: pointer;
  padding: 0;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 6px;
  transition: background 0.3s ease;

  &:hover {
    background: #f3f4f6;
  }
}

.modal-body {
  padding: 24px;
}

.usage-options {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-top: 16px;
}

.usage-option {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: left;
  width: 100%;

  &:hover {
    background: #f3f4f6;
    border-color: #4f46e5;
  }

  svg {
    color: #4f46e5;
    flex-shrink: 0;
  }

  span {
    font-weight: 500;
    color: #374151;
  }
}



/* Responsive Design */
@media (max-width: 768px) {
  .container {
    padding: 0 20px;
  }

  .header-title .title {
    font-size: 32px;
  }

  .services-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .service-stats {
    gap: 16px;
  }

  .filters {
    flex-direction: column;
    align-items: center;
  }

  .filter-select {
    width: 100%;
    max-width: 300px;
  }

  .search-bar {
    margin: 0 auto 16px;
  }

  .header-actions {
    flex-direction: column;
    align-items: center;
  }

  .service-actions {
    flex-direction: column;
  }

  .modal-content {
    margin: 20px;
    width: calc(100% - 40px);
  }
}

@media (min-width: 1920px) {
  .services-grid {
    grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  }
}
</style>
