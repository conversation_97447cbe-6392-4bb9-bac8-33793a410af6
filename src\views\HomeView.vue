<template>
  <div class="mcp-services">
    <!-- Floating Particles -->
    <div class="particles">
      <div class="particle" v-for="n in 20" :key="n" :style="getParticleStyle()"></div>
    </div>

    <!-- Top Navigation -->
    <nav class="top-nav">
      <div class="container">
        <div class="nav-content">
          <div class="nav-left">
            <div class="logo">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <path d="M12 2L2 7l10 5 10-5-10-5z"/>
                <path d="M2 17l10 5 10-5"/>
                <path d="M2 12l10 5 10-5"/>
              </svg>
              <span>MCP 服务广场</span>
            </div>
            <div class="nav-search">
              <input
                v-model="searchQuery"
                type="text"
                placeholder="搜索服务、作者或关键词..."
                class="search-input"
                @input="filterServices"
              />
              <svg class="search-icon" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <circle cx="11" cy="11" r="8"></circle>
                <path d="m21 21-4.35-4.35"></path>
              </svg>
            </div>
          </div>
          <div class="nav-right">
            <button v-if="!authStore.isLoggedIn" class="btn btn-primary" @click="goToAuth">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <path d="M12 5v14m-7-7h14"/>
              </svg>
              发布服务
            </button>
            <button v-else class="btn btn-primary" @click="goToPublish">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <path d="M12 5v14m-7-7h14"/>
              </svg>
              发布服务
            </button>
            <button class="btn btn-secondary" @click="refreshServices">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <path d="M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8"/>
                <path d="M21 3v5h-5"/>
                <path d="M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16"/>
                <path d="M3 21v-5h5"/>
              </svg>
              刷新
            </button>
            <button v-if="authStore.isLoggedIn" class="btn btn-ghost" @click="logout">退出登录</button>
          </div>
        </div>
      </div>
    </nav>

    <!-- Main Content -->
    <div class="main-content">
      <div class="container">
        <div class="content-layout">
          <!-- Sidebar -->
          <aside class="sidebar">
            <div class="sidebar-section">
              <h3>分类筛选</h3>
              <div class="category-filters">
                <label class="filter-item" :class="{ active: selectedCategory === '' }">
                  <input type="radio" v-model="selectedCategory" value="" @change="filterServices" />
                  <span class="filter-text">
                    <span class="filter-icon">📦</span>
                    全部服务
                  </span>
                  <span class="filter-count">{{ services.length }}</span>
                </label>
                <label class="filter-item" :class="{ active: selectedCategory === 'ai' }">
                  <input type="radio" v-model="selectedCategory" value="ai" @change="filterServices" />
                  <span class="filter-text">
                    <span class="filter-icon">🤖</span>
                    AI 助手
                  </span>
                  <span class="filter-count">{{ getCategoryCount('ai') }}</span>
                </label>
                <label class="filter-item" :class="{ active: selectedCategory === 'data' }">
                  <input type="radio" v-model="selectedCategory" value="data" @change="filterServices" />
                  <span class="filter-text">
                    <span class="filter-icon">📊</span>
                    数据处理
                  </span>
                  <span class="filter-count">{{ getCategoryCount('data') }}</span>
                </label>
                <label class="filter-item" :class="{ active: selectedCategory === 'web' }">
                  <input type="radio" v-model="selectedCategory" value="web" @change="filterServices" />
                  <span class="filter-text">
                    <span class="filter-icon">🌐</span>
                    网络服务
                  </span>
                  <span class="filter-count">{{ getCategoryCount('web') }}</span>
                </label>
                <label class="filter-item" :class="{ active: selectedCategory === 'tool' }">
                  <input type="radio" v-model="selectedCategory" value="tool" @change="filterServices" />
                  <span class="filter-text">
                    <span class="filter-icon">🔧</span>
                    工具类
                  </span>
                  <span class="filter-count">{{ getCategoryCount('tool') }}</span>
                </label>
              </div>
            </div>

            <div class="sidebar-section">
              <h3>排序方式</h3>
              <select v-model="sortBy" @change="sortServices" class="sort-select">
                <option value="latest">最新发布</option>
                <option value="popular">最受欢迎</option>
                <option value="rating">评分最高</option>
                <option value="name">名称排序</option>
              </select>
            </div>

            <div class="sidebar-section">
              <h3>统计信息</h3>
              <div class="stats-overview">
                <div class="stat-item">
                  <span class="stat-number">{{ services.length }}</span>
                  <span class="stat-label">总服务数</span>
                </div>
                <div class="stat-item">
                  <span class="stat-number">{{ getTotalDownloads() }}</span>
                  <span class="stat-label">总下载量</span>
                </div>
                <div class="stat-item">
                  <span class="stat-number">{{ getAverageRating() }}</span>
                  <span class="stat-label">平均评分</span>
                </div>
              </div>
            </div>
          </aside>

          <!-- Services List -->
          <main class="services-main">
            <div class="services-header">
              <div class="results-info">
                <h2>{{ getResultsTitle() }}</h2>
                <p>{{ filteredServices.length }} 个服务</p>
              </div>
            </div>

            <!-- Services Grid -->
            <div class="services-grid">
              <div
                v-for="service in filteredServices"
                :key="service.id"
                class="service-card"
                @click="viewService(service)"
              >
                <div class="service-header">
                  <div class="service-icon">
                    <img :src="service.icon || '/default-icon.png'" :alt="service.name" />
                  </div>
                  <div class="service-info">
                    <h3 class="service-name">{{ service.name }}</h3>
                    <p class="service-author">{{ service.author }}</p>
                    <div class="service-meta">
                      <span class="category-tag" :class="service.category_id">{{ getCategoryName(service.category_id) }}</span>
                      <span class="version-tag">v{{ service.version || '1.0.0' }}</span>
                      <span class="update-time">{{ getRelativeTime(service.updated_at) }}</span>
                    </div>
                  </div>
                  <div class="service-actions">
                    <button class="btn btn-sm btn-primary" @click.stop="useService(service)">
                      <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                        <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
                        <polyline points="7,10 12,15 17,10"/>
                        <line x1="12" y1="15" x2="12" y2="3"/>
                      </svg>
                      使用
                    </button>
                    <button class="btn btn-sm btn-ghost" @click.stop="viewDetails(service)">详情</button>
                  </div>
                </div>
                <div class="service-description">
                  <p>{{ service.description }}</p>
                </div>
                <div class="service-footer">
                  <div class="service-stats">
                    <span class="stat">
                      <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                        <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
                        <polyline points="7,10 12,15 17,10"/>
                        <line x1="12" y1="15" x2="12" y2="3"/>
                      </svg>
                      {{ formatNumber(service.downloads || 0) }}
                    </span>
                    <span class="stat">
                      <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                        <polygon points="12,2 15.09,8.26 22,9.27 17,14.14 18.18,21.02 12,17.77 5.82,21.02 7,14.14 2,9.27 8.91,8.26"/>
                      </svg>
                      {{ service.rating || 0 }}
                    </span>
                    <span class="stat">
                      <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                        <circle cx="12" cy="12" r="3"/>
                        <path d="M12 1v6m0 6v6m11-7h-6m-6 0H1"/>
                      </svg>
                      {{ service.size || 'N/A' }}
                    </span>
                  </div>
                  <div class="service-tags" v-if="service.tags">
                    <span v-for="tag in service.tags?.slice(0, 3)" :key="tag" class="tag">{{ tag }}</span>
                  </div>
                </div>
              </div>
            </div>

            <!-- Loading State -->
            <div v-if="loading" class="loading">
              <div class="spinner"></div>
              <p>加载中...</p>
            </div>

            <!-- Empty State -->
            <div v-if="!loading && filteredServices.length === 0" class="empty-state">
              <div class="empty-icon">📦</div>
              <h3>暂无服务</h3>
              <p>{{ searchQuery ? '没有找到匹配的服务' : '还没有发布的 MCP 服务' }}</p>
            </div>
          </main>
        </div>
      </div>
    </div>

    <!-- Service Usage Modal -->
    <div v-if="showUsageModal && selectedService" class="modal-overlay" @click="closeUsageModal">
      <div class="modal-content" @click.stop>
        <div class="modal-header">
          <h3>使用 {{ selectedService.name }}</h3>
          <button class="modal-close" @click="closeUsageModal">×</button>
        </div>
        <div class="modal-body">
          <p>选择使用方式：</p>
          <div class="usage-options">
            <button class="usage-option" @click="downloadService">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
                <polyline points="7,10 12,15 17,10"/>
                <line x1="12" y1="15" x2="12" y2="3"/>
              </svg>
              <span>下载到本地</span>
            </button>
            <button class="usage-option" @click="copyInstallCommand">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <rect width="14" height="14" x="8" y="8" rx="2" ry="2"/>
                <path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"/>
              </svg>
              <span>复制安装命令</span>
            </button>
            <button class="usage-option" @click="openServiceInBrowser">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"/>
                <polyline points="15,3 21,3 21,9"/>
                <line x1="10" y1="14" x2="21" y2="3"/>
              </svg>
              <span>在浏览器中打开</span>
            </button>
            <button class="usage-option" @click="viewServiceDetails">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <path d="M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z"/>
                <path d="M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z"/>
              </svg>
              <span>查看详细信息</span>
            </button>
          </div>
        </div>
      </div>
    </div>

  </div>
</template>

<script>
import { useAuthStore } from '@/stores/auth'
import { servicesAPI, categoriesAPI } from '@/services/api'

export default {
  name: 'HomeView',
  data() {
    return {
      services: [],
      filteredServices: [],
      categories: [],
      searchQuery: '',
      selectedCategory: '',
      sortBy: 'latest',
      loading: false,
      authStore: useAuthStore(),
      showUsageModal: false,
      selectedService: null
    }
  },
  async mounted() {
    this.authStore = useAuthStore()
    await this.loadServices()
    await this.loadCategories()
  },
  methods: {
    async loadServices() {
      this.loading = true
      try {
        const params = {
          page: 1,
          limit: 50,
          sort: this.sortBy
        }

        if (this.selectedCategory) {
          params.category = this.selectedCategory
        }

        if (this.searchQuery) {
          params.search = this.searchQuery
        }

        const response = await servicesAPI.getServices(params)
        this.services = response.data.services || []
        this.filteredServices = [...this.services]
      } catch (error) {
        console.error('Failed to load services:', error)
      } finally {
        this.loading = false
      }
    },

    async loadCategories() {
      try {
        const response = await categoriesAPI.getCategories()
        this.categories = response.data.categories || []
      } catch (error) {
        console.error('Failed to load categories:', error)
      }
    },
    async filterServices() {
      await this.loadServices()
    },
    async sortServices() {
      await this.loadServices()
    },
    getCategoryName(category) {
      const categories = {
        ai: 'AI 助手',
        data: '数据处理',
        web: '网络服务',
        tool: '工具类'
      }
      return categories[category] || category
    },

    getCategoryCount(category) {
      const categoryData = this.categories.find(cat => cat.id === category)
      return categoryData ? categoryData.service_count : 0
    },

    getTotalDownloads() {
      const total = this.services.reduce((sum, service) => sum + (service.downloads || 0), 0)
      return this.formatNumber(total)
    },

    getAverageRating() {
      const ratings = this.services.filter(service => service.rating).map(service => service.rating)
      if (ratings.length === 0) return '0.0'
      const average = ratings.reduce((sum, rating) => sum + rating, 0) / ratings.length
      return average.toFixed(1)
    },

    getResultsTitle() {
      if (this.searchQuery) {
        return `搜索 "${this.searchQuery}" 的结果`
      }
      if (this.selectedCategory) {
        return this.getCategoryName(this.selectedCategory)
      }
      return '所有服务'
    },

    formatNumber(num) {
      if (num >= 1000000) {
        return (num / 1000000).toFixed(1) + 'M'
      }
      if (num >= 1000) {
        return (num / 1000).toFixed(1) + 'K'
      }
      return num.toString()
    },

    getRelativeTime(date) {
      const now = new Date()
      const diffTime = Math.abs(now - new Date(date))
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))

      if (diffDays === 1) return '1天前'
      if (diffDays < 7) return `${diffDays}天前`
      if (diffDays < 30) return `${Math.ceil(diffDays / 7)}周前`
      if (diffDays < 365) return `${Math.ceil(diffDays / 30)}月前`
      return `${Math.ceil(diffDays / 365)}年前`
    },

    formatDate(date) {
      return new Date(date).toLocaleDateString('zh-CN')
    },

    getParticleStyle() {
      const size = Math.random() * 4 + 2
      const left = Math.random() * 100
      const animationDelay = Math.random() * 20
      const animationDuration = Math.random() * 10 + 15

      return {
        width: `${size}px`,
        height: `${size}px`,
        left: `${left}%`,
        animationDelay: `${animationDelay}s`,
        animationDuration: `${animationDuration}s`
      }
    },
    refreshServices() {
      this.loadServices()
    },
    viewService(service) {
      // Navigate to service detail page
      this.$router.push({ name: 'service-detail', params: { id: service.id } })
    },
    useService(service) {
      this.selectedService = service
      this.showUsageModal = true
    },
    viewDetails(service) {
      // Navigate to service detail page
      this.$router.push({ name: 'service-detail', params: { id: service.id } })
    },
    goToAuth() {
      this.$router.push('/auth')
    },
    goToPublish() {
      this.$router.push('/publish')
    },
    async logout() {
      await this.authStore.logout()
      // Optionally show a message or refresh the page
    },

    closeUsageModal() {
      this.showUsageModal = false
      this.selectedService = null
    },

    async downloadService() {
      if (!this.selectedService) return

      // Mock download functionality
      console.log('下载服务:', this.selectedService.name)

      // Create a mock download
      const link = document.createElement('a')
      link.href = `#download-${this.selectedService.id}`
      link.download = `${this.selectedService.name}.mcp`
      link.click()

      this.closeUsageModal()
    },

    async copyInstallCommand() {
      if (!this.selectedService) return

      const command = `# 安装 ${this.selectedService.name}\nnpm install @mcp/${this.selectedService.name.toLowerCase().replace(/\s+/g, '-')}\n\n# 或使用 yarn\nyarn add @mcp/${this.selectedService.name.toLowerCase().replace(/\s+/g, '-')}`

      try {
        await navigator.clipboard.writeText(command)
        console.log('安装命令已复制到剪贴板')
        // You could add a toast notification here
      } catch (error) {
        console.error('复制失败:', error)
      }

      this.closeUsageModal()
    },

    openServiceInBrowser() {
      if (!this.selectedService) return

      // Open service repository or documentation
      const url = `https://github.com/mcp-services/${this.selectedService.name.toLowerCase().replace(/\s+/g, '-')}`
      window.open(url, '_blank')

      this.closeUsageModal()
    },

    viewServiceDetails() {
      if (!this.selectedService) return

      this.closeUsageModal()
      this.$router.push({ name: 'service-detail', params: { id: this.selectedService.id } })
    }
  }
}
</script>

<style lang="scss" scoped>
.mcp-services {
  min-height: 100vh;
  background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 25%, #16213e 50%, #0f3460 75%, #533483 100%);
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
      radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
      radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.15) 0%, transparent 50%),
      radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.1) 0%, transparent 50%);
    animation: backgroundShift 20s ease-in-out infinite;
    pointer-events: none;
    z-index: 1;
  }

  &::after {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
      linear-gradient(90deg, rgba(56, 189, 248, 0.03) 1px, transparent 1px),
      linear-gradient(180deg, rgba(56, 189, 248, 0.03) 1px, transparent 1px);
    background-size: 50px 50px;
    animation: gridMove 30s linear infinite;
    pointer-events: none;
    z-index: 1;
  }
}

@keyframes backgroundShift {
  0%, 100% {
    transform: translateX(0) translateY(0) scale(1);
  }
  25% {
    transform: translateX(-20px) translateY(-10px) scale(1.05);
  }
  50% {
    transform: translateX(20px) translateY(20px) scale(0.95);
  }
  75% {
    transform: translateX(-10px) translateY(10px) scale(1.02);
  }
}

@keyframes gridMove {
  0% {
    transform: translate(0, 0);
  }
  100% {
    transform: translate(50px, 50px);
  }
}

/* Floating Particles */
.particles {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 2;
}

.particle {
  position: absolute;
  background: radial-gradient(circle, rgba(56, 189, 248, 0.8) 0%, rgba(56, 189, 248, 0.2) 70%, transparent 100%);
  border-radius: 50%;
  animation: float linear infinite;
  box-shadow: 0 0 10px rgba(56, 189, 248, 0.5);
}

@keyframes float {
  0% {
    transform: translateY(100vh) rotate(0deg);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    transform: translateY(-100px) rotate(360deg);
    opacity: 0;
  }
}

.container {
  width: 100%;
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 24px;
}

/* Top Navigation */
.top-nav {
  background: rgba(15, 15, 35, 0.95);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(56, 189, 248, 0.2);
  padding: 16px 0;
  position: sticky;
  top: 0;
  z-index: 100;
  box-shadow: 0 4px 32px rgba(0, 0, 0, 0.3);
}

.nav-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 24px;
}

.nav-left {
  display: flex;
  align-items: center;
  gap: 24px;
  flex: 1;
}

.logo {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 18px;
  font-weight: 600;
  color: #ffffff;
  text-shadow: 0 0 10px rgba(56, 189, 248, 0.5);

  svg {
    color: #38bdf8;
    filter: drop-shadow(0 0 8px rgba(56, 189, 248, 0.6));
  }
}

.nav-search {
  position: relative;
  max-width: 400px;
  flex: 1;

  .search-input {
    width: 100%;
    padding: 8px 12px 8px 36px;
    border: 1px solid rgba(56, 189, 248, 0.3);
    border-radius: 8px;
    font-size: 14px;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    color: #ffffff;
    transition: all 0.3s ease;

    &:focus {
      outline: none;
      border-color: #38bdf8;
      background: rgba(255, 255, 255, 0.15);
      box-shadow: 0 0 0 3px rgba(56, 189, 248, 0.2), 0 0 20px rgba(56, 189, 248, 0.3);
    }

    &::placeholder {
      color: rgba(255, 255, 255, 0.7);
    }
  }

  .search-icon {
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: rgba(255, 255, 255, 0.7);
  }
}

.nav-right {
  display: flex;
  align-items: center;
  gap: 12px;
}

/* Button Styles */
.btn {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 6px 16px;
  border: 1px solid transparent;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  white-space: nowrap;

  &-primary {
    background: linear-gradient(135deg, #38bdf8 0%, #3b82f6 50%, #8b5cf6 100%);
    color: white;
    border-color: #38bdf8;
    box-shadow: 0 0 20px rgba(56, 189, 248, 0.4);
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
      transition: left 0.5s;
    }

    &:hover {
      background: linear-gradient(135deg, #0ea5e9 0%, #2563eb 50%, #7c3aed 100%);
      box-shadow: 0 0 30px rgba(56, 189, 248, 0.6);
      transform: translateY(-1px);

      &::before {
        left: 100%;
      }
    }
  }

  &-secondary {
    background: rgba(255, 255, 255, 0.1);
    color: #ffffff;
    border-color: rgba(56, 189, 248, 0.3);
    backdrop-filter: blur(10px);

    &:hover {
      background: rgba(255, 255, 255, 0.2);
      border-color: rgba(56, 189, 248, 0.5);
      box-shadow: 0 0 15px rgba(56, 189, 248, 0.3);
    }
  }

  &-ghost {
    background: transparent;
    color: rgba(255, 255, 255, 0.8);
    border-color: transparent;

    &:hover {
      background: rgba(255, 255, 255, 0.1);
      color: #ffffff;
      box-shadow: 0 0 10px rgba(56, 189, 248, 0.2);
    }
  }

  &-sm {
    padding: 4px 8px;
    font-size: 12px;
  }
}

/* Main Content Layout */
.main-content {
  padding: 24px 0;
}

.content-layout {
  display: grid;
  grid-template-columns: 280px 1fr;
  gap: 32px;
  align-items: start;
}

/* Sidebar */
.sidebar {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(56, 189, 248, 0.2);
  border-radius: 12px;
  padding: 24px;
  position: sticky;
  height: fit-content;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  position: relative;
  z-index: 10;
}

.sidebar-section {
  margin-bottom: 32px;

  &:last-child {
    margin-bottom: 0;
  }

  h3 {
    font-size: 16px;
    font-weight: 600;
    color: #ffffff;
    margin: 0 0 16px 0;
    padding-bottom: 8px;
    border-bottom: 1px solid rgba(56, 189, 248, 0.3);
    text-shadow: 0 0 10px rgba(56, 189, 248, 0.5);
  }
}

.category-filters {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.filter-item {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  color: rgba(255, 255, 255, 0.8);

  &:hover {
    background: rgba(56, 189, 248, 0.1);
    color: #ffffff;
    box-shadow: 0 0 15px rgba(56, 189, 248, 0.2);
  }

  &.active {
    background: linear-gradient(135deg, rgba(56, 189, 248, 0.2), rgba(59, 130, 246, 0.2));
    color: #38bdf8;
    border: 1px solid rgba(56, 189, 248, 0.3);
    box-shadow: 0 0 20px rgba(56, 189, 248, 0.3);
  }

  input[type="radio"] {
    display: none;
  }

  .filter-text {
    display: flex;
    align-items: center;
    gap: 8px;
    flex: 1;
    font-size: 14px;
  }

  .filter-icon {
    font-size: 16px;
  }

  .filter-count {
    font-size: 12px;
    color: rgba(255, 255, 255, 0.6);
    background: rgba(255, 255, 255, 0.1);
    padding: 2px 6px;
    border-radius: 12px;
    min-width: 20px;
    text-align: center;
    border: 1px solid rgba(255, 255, 255, 0.2);
  }

  &.active .filter-count {
    background: rgba(56, 189, 248, 0.3);
    color: #38bdf8;
    border-color: rgba(56, 189, 248, 0.5);
  }
}

.sort-select {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid rgba(56, 189, 248, 0.3);
  border-radius: 8px;
  font-size: 14px;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  color: #ffffff;
  cursor: pointer;

  &:focus {
    outline: none;
    border-color: #38bdf8;
    box-shadow: 0 0 0 3px rgba(56, 189, 248, 0.2);
  }

  option {
    background: #1a1a2e;
    color: #ffffff;
  }
}

.stats-overview {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 12px;
  background: rgba(56, 189, 248, 0.1);
  border: 1px solid rgba(56, 189, 248, 0.2);
  border-radius: 8px;
  text-align: center;
  backdrop-filter: blur(10px);

  .stat-number {
    font-size: 20px;
    font-weight: 600;
    color: #38bdf8;
    text-shadow: 0 0 10px rgba(56, 189, 248, 0.5);
  }

  .stat-label {
    font-size: 12px;
    color: rgba(255, 255, 255, 0.8);
    margin-top: 4px;
  }
}

/* Services Main */
.services-main {
  flex: 1;
}

.services-header {
  margin-bottom: 24px;

  .results-info {
    h2 {
      font-size: 24px;
      font-weight: 600;
      color: #ffffff;
      margin: 0 0 4px 0;
      text-shadow: 0 0 15px rgba(56, 189, 248, 0.5);
    }

    p {
      font-size: 14px;
      color: rgba(255, 255, 255, 0.8);
      margin: 0;
    }
  }
}

.services-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 16px;
}

.service-card {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(56, 189, 248, 0.2);
  border-radius: 12px;
  padding: 16px;
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  z-index: 10;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(56, 189, 248, 0.1) 0%, rgba(147, 51, 234, 0.1) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
  }

  &:hover {
    border-color: rgba(56, 189, 248, 0.4);
    box-shadow: 0 8px 32px rgba(56, 189, 248, 0.2);
    transform: translateY(-2px);

    &::before {
      opacity: 1;
    }
  }
}

.service-header {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  margin-bottom: 12px;
}

.service-icon {
  width: 40px;
  height: 40px;
  border-radius: 6px;
  overflow: hidden;
  flex-shrink: 0;
  background: #f6f8fa;
  display: flex;
  align-items: center;
  justify-content: center;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

.service-info {
  flex: 1;
  min-width: 0;
}

.service-name {
  font-size: 16px;
  font-weight: 600;
  color: #38bdf8;
  margin: 0 0 2px 0;
  line-height: 1.3;
  text-shadow: 0 0 10px rgba(56, 189, 248, 0.5);
  position: relative;
  z-index: 1;

  &:hover {
    color: #0ea5e9;
    text-shadow: 0 0 15px rgba(56, 189, 248, 0.8);
  }
}

.service-author {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.7);
  margin: 0 0 6px 0;
  position: relative;
  z-index: 1;
}

.service-meta {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.service-actions {
  display: flex;
  gap: 6px;
  margin-left: auto;
}

.category-tag {
  display: inline-block;
  padding: 2px 6px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 500;
  border: 1px solid;

  &.ai {
    background: #dbeafe;
    color: #1e40af;
    border-color: #bfdbfe;
  }

  &.data {
    background: #dcfce7;
    color: #166534;
    border-color: #bbf7d0;
  }

  &.web {
    background: #fef3c7;
    color: #92400e;
    border-color: #fde68a;
  }

  &.tool {
    background: #e0e7ff;
    color: #3730a3;
    border-color: #c7d2fe;
  }
}

.version-tag {
  font-size: 11px;
  color: #656d76;
  background: #f6f8fa;
  padding: 2px 6px;
  border-radius: 12px;
  border: 1px solid #d0d7de;
}

.update-time {
  font-size: 11px;
  color: #656d76;
}

.service-description {
  margin-bottom: 12px;
  height: 42px;
  p {
    color: rgba(255, 255, 255, 0.8);
    line-height: 1.5;
    margin: 0;
    font-size: 14px;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    position: relative;
    z-index: 1;
  }
}

.service-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 12px;
  margin-top: 12px;
  padding-top: 12px;
  border-top: 1px solid rgba(56, 189, 248, 0.2);
  position: relative;
  z-index: 1;
}

.service-stats {
  display: flex;
  gap: 16px;
  align-items: center;
}

.stat {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: rgba(255, 255, 255, 0.7);

  svg {
    color: rgba(56, 189, 248, 0.8);
  }
}

.service-tags {
  display: flex;
  gap: 4px;
  flex-wrap: wrap;
}

.tag {
  font-size: 11px;
  color: rgba(255, 255, 255, 0.8);
  background: rgba(56, 189, 248, 0.1);
  padding: 2px 6px;
  border-radius: 12px;
  border: 1px solid rgba(56, 189, 248, 0.3);
  backdrop-filter: blur(5px);
}

/* Loading and Empty States */
.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 64px 32px;
  color: rgba(255, 255, 255, 0.8);

  .spinner {
    width: 32px;
    height: 32px;
    border: 3px solid rgba(56, 189, 248, 0.2);
    border-top: 3px solid #38bdf8;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 16px;
    box-shadow: 0 0 20px rgba(56, 189, 248, 0.4);
  }

  p {
    font-size: 14px;
    margin: 0;
  }
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 64px 32px;
  text-align: center;
  color: rgba(255, 255, 255, 0.8);

  .empty-icon {
    font-size: 48px;
    margin-bottom: 16px;
    opacity: 0.5;
    filter: drop-shadow(0 0 10px rgba(56, 189, 248, 0.3));
  }

  h3 {
    font-size: 18px;
    font-weight: 600;
    margin: 0 0 8px 0;
    color: #ffffff;
    text-shadow: 0 0 10px rgba(56, 189, 248, 0.5);
  }

  p {
    font-size: 14px;
    margin: 0;
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 1200px) {
  .content-layout {
    grid-template-columns: 240px 1fr;
    gap: 24px;
  }

  .services-grid {
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  }
}

@media (max-width: 768px) {
  .container {
    padding: 0 16px;
  }

  .nav-content {
    flex-direction: column;
    gap: 16px;
  }

  .nav-left {
    width: 100%;
  }

  .nav-search {
    max-width: none;
  }

  .content-layout {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .sidebar {
    position: static;
    order: 2;
  }

  .services-grid {
    grid-template-columns: 1fr;
  }

  .service-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .service-actions {
    margin-left: 0;
    margin-top: 8px;
  }
}

/* Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 12px;
  width: 90%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #e5e7eb;

  h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: #1f2937;
  }
}

.modal-close {
  background: none;
  border: none;
  font-size: 24px;
  color: #6b7280;
  cursor: pointer;
  padding: 0;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 6px;
  transition: background 0.3s ease;

  &:hover {
    background: #f3f4f6;
  }
}

.modal-body {
  padding: 24px;
}

.usage-options {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-top: 16px;
}

.usage-option {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: left;
  width: 100%;

  &:hover {
    background: #f3f4f6;
    border-color: #4f46e5;
  }

  svg {
    color: #4f46e5;
    flex-shrink: 0;
  }

  span {
    font-weight: 500;
    color: #374151;
  }
}



/* Responsive Design */
@media (max-width: 768px) {
  .container {
    padding: 0 20px;
  }

  .header-title .title {
    font-size: 32px;
  }

  .services-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .service-stats {
    gap: 16px;
  }

  .filters {
    flex-direction: column;
    align-items: center;
  }

  .filter-select {
    width: 100%;
    max-width: 300px;
  }

  .search-bar {
    margin: 0 auto 16px;
  }

  .header-actions {
    flex-direction: column;
    align-items: center;
  }

  .service-actions {
    flex-direction: column;
  }

  .modal-content {
    margin: 20px;
    width: calc(100% - 40px);
  }
}

@media (min-width: 1920px) {
  .services-grid {
    grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  }
}
</style>
