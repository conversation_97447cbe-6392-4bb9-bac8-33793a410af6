<template>
  <div class="mcp-services">
    <!-- Header Section -->
    <header class="header">
      <div class="container">
        <div class="header-content">
          <div class="header-title">
            <p class="title">MCP 服务广场</p>
            <p class="subtitle">发现和使用优质的 Model Context Protocol 服务</p>
          </div>
          <div class="header-actions">
            <button v-if="!authStore.isLoggedIn" class="btn btn-primary" @click="goToAuth">发布服务</button>
            <button v-else class="btn btn-primary" @click="goToPublish">发布服务</button>
            <button v-if="authStore.isLoggedIn" class="btn btn-secondary" @click="logout">退出登录</button>
            <button class="btn btn-secondary" @click="refreshServices">刷新</button>
          </div>
        </div>
      </div>
    </header>

    <!-- Search and Filter Section -->
    <section class="search-section">
      <div class="container">
        <div class="search-bar">
          <input
            v-model="searchQuery"
            type="text"
            placeholder="搜索 MCP 服务..."
            class="search-input"
            @input="filterServices"
          />
          <button class="search-btn">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <circle cx="11" cy="11" r="8"></circle>
              <path d="m21 21-4.35-4.35"></path>
            </svg>
          </button>
        </div>
        <div class="filters">
          <select v-model="selectedCategory" @change="filterServices" class="filter-select">
            <option value="">所有分类</option>
            <option value="ai">AI 助手</option>
            <option value="data">数据处理</option>
            <option value="web">网络服务</option>
            <option value="tool">工具类</option>
          </select>
          <select v-model="sortBy" @change="sortServices" class="filter-select">
            <option value="latest">最新发布</option>
            <option value="popular">最受欢迎</option>
            <option value="name">名称排序</option>
          </select>
        </div>
      </div>
    </section>

    <!-- Services Grid -->
    <section class="services-section">
      <div class="container">
        <div class="services-grid">
          <div
            v-for="service in filteredServices"
            :key="service.id"
            class="service-card"
            @click="viewService(service)"
          >
            <div class="service-header">
              <div class="service-icon">
                <img :src="service.icon || '/default-icon.png'" :alt="service.name" />
              </div>
              <div class="service-info">
                <h3 class="service-name">{{ service.name }}</h3>
                <p class="service-author">by {{ service.author }}</p>
              </div>
              <div class="service-category">
                <span class="category-tag" :class="service.category">{{ getCategoryName(service.category) }}</span>
              </div>
            </div>
            <div class="service-description">
              <p>{{ service.description }}</p>
            </div>
            <div class="service-stats">
              <div class="stat">
                <span class="stat-icon">👥</span>
                <span class="stat-value">{{ service.downloads || 0 }}</span>
                <span class="stat-label">下载</span>
              </div>
              <div class="stat">
                <span class="stat-icon">⭐</span>
                <span class="stat-value">{{ service.rating || 0 }}</span>
                <span class="stat-label">评分</span>
              </div>
              <div class="stat">
                <span class="stat-icon">📅</span>
                <span class="stat-value">{{ formatDate(service.updatedAt) }}</span>
                <span class="stat-label">更新</span>
              </div>
            </div>
            <div class="service-actions">
              <button class="btn btn-primary btn-sm" @click.stop="useService(service)">
                使用服务
              </button>
              <button class="btn btn-secondary btn-sm" @click.stop="viewDetails(service)">
                查看详情
              </button>
            </div>
          </div>
        </div>

        <!-- Loading State -->
        <div v-if="loading" class="loading">
          <div class="spinner"></div>
          <p>加载中...</p>
        </div>

        <!-- Empty State -->
        <div v-if="!loading && filteredServices.length === 0" class="empty-state">
          <div class="empty-icon">📦</div>
          <h3>暂无服务</h3>
          <p>{{ searchQuery ? '没有找到匹配的服务' : '还没有发布的 MCP 服务' }}</p>
        </div>
      </div>
    </section>

    <!-- Service Usage Modal -->
    <div v-if="showUsageModal && selectedService" class="modal-overlay" @click="closeUsageModal">
      <div class="modal-content" @click.stop>
        <div class="modal-header">
          <h3>使用 {{ selectedService.name }}</h3>
          <button class="modal-close" @click="closeUsageModal">×</button>
        </div>
        <div class="modal-body">
          <p>选择使用方式：</p>
          <div class="usage-options">
            <button class="usage-option" @click="downloadService">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
                <polyline points="7,10 12,15 17,10"/>
                <line x1="12" y1="15" x2="12" y2="3"/>
              </svg>
              <span>下载到本地</span>
            </button>
            <button class="usage-option" @click="copyInstallCommand">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <rect width="14" height="14" x="8" y="8" rx="2" ry="2"/>
                <path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"/>
              </svg>
              <span>复制安装命令</span>
            </button>
            <button class="usage-option" @click="openServiceInBrowser">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"/>
                <polyline points="15,3 21,3 21,9"/>
                <line x1="10" y1="14" x2="21" y2="3"/>
              </svg>
              <span>在浏览器中打开</span>
            </button>
            <button class="usage-option" @click="viewServiceDetails">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <path d="M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z"/>
                <path d="M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z"/>
              </svg>
              <span>查看详细信息</span>
            </button>
          </div>
        </div>
      </div>
    </div>

  </div>
</template>

<script>
import { useAuthStore } from '@/stores/auth'

export default {
  name: 'HomeView',
  data() {
    return {
      services: [],
      filteredServices: [],
      searchQuery: '',
      selectedCategory: '',
      sortBy: 'latest',
      loading: false,
      authStore: useAuthStore(),
      showUsageModal: false,
      selectedService: null
    }
  },
  mounted() {
    this.loadServices()
  },
  methods: {
    async loadServices() {
      this.loading = true
      try {
        // Mock data for demonstration
        await new Promise(resolve => setTimeout(resolve, 1000))
        this.services = [
          {
            id: 1,
            name: 'AI 代码助手',
            author: 'OpenAI',
            description: '智能代码生成和优化工具，支持多种编程语言',
            category: 'ai',
            downloads: 1250,
            rating: 4.8,
            updatedAt: new Date('2024-01-15'),
            icon: '/api/placeholder/64/64'
          },
          {
            id: 2,
            name: '数据分析器',
            author: 'DataCorp',
            description: '强大的数据处理和可视化工具',
            category: 'data',
            downloads: 890,
            rating: 4.6,
            updatedAt: new Date('2024-01-10'),
            icon: '/api/placeholder/64/64'
          },
          {
            id: 3,
            name: 'Web 爬虫',
            author: 'WebTools',
            description: '高效的网页数据抓取工具',
            category: 'web',
            downloads: 2100,
            rating: 4.9,
            updatedAt: new Date('2024-01-20'),
            icon: '/api/placeholder/64/64'
          },
          {
            id: 4,
            name: '文档生成器',
            author: 'DocGen',
            description: '自动生成项目文档和API文档',
            category: 'tool',
            downloads: 567,
            rating: 4.4,
            updatedAt: new Date('2024-01-08'),
            icon: '/api/placeholder/64/64'
          }
        ]
        this.filteredServices = [...this.services]
      } catch (error) {
        console.error('Failed to load services:', error)
      } finally {
        this.loading = false
      }
    },
    filterServices() {
      let filtered = [...this.services]

      if (this.searchQuery) {
        filtered = filtered.filter(service =>
          service.name.toLowerCase().includes(this.searchQuery.toLowerCase()) ||
          service.description.toLowerCase().includes(this.searchQuery.toLowerCase()) ||
          service.author.toLowerCase().includes(this.searchQuery.toLowerCase())
        )
      }

      if (this.selectedCategory) {
        filtered = filtered.filter(service => service.category === this.selectedCategory)
      }

      this.filteredServices = filtered
      this.sortServices()
    },
    sortServices() {
      switch (this.sortBy) {
        case 'latest':
          this.filteredServices.sort((a, b) => new Date(b.updatedAt) - new Date(a.updatedAt))
          break
        case 'popular':
          this.filteredServices.sort((a, b) => b.downloads - a.downloads)
          break
        case 'name':
          this.filteredServices.sort((a, b) => a.name.localeCompare(b.name))
          break
      }
    },
    getCategoryName(category) {
      const categories = {
        ai: 'AI 助手',
        data: '数据处理',
        web: '网络服务',
        tool: '工具类'
      }
      return categories[category] || category
    },
    formatDate(date) {
      return new Date(date).toLocaleDateString('zh-CN')
    },
    refreshServices() {
      this.loadServices()
    },
    viewService(service) {
      // Navigate to service detail page
      this.$router.push({ name: 'service-detail', params: { id: service.id } })
    },
    useService(service) {
      this.selectedService = service
      this.showUsageModal = true
    },
    viewDetails(service) {
      // Navigate to service detail page
      this.$router.push({ name: 'service-detail', params: { id: service.id } })
    },
    goToAuth() {
      this.$router.push('/auth')
    },
    goToPublish() {
      this.$router.push('/publish')
    },
    async logout() {
      await this.authStore.logout()
      // Optionally show a message or refresh the page
    },

    closeUsageModal() {
      this.showUsageModal = false
      this.selectedService = null
    },

    async downloadService() {
      if (!this.selectedService) return

      // Mock download functionality
      console.log('下载服务:', this.selectedService.name)

      // Create a mock download
      const link = document.createElement('a')
      link.href = `#download-${this.selectedService.id}`
      link.download = `${this.selectedService.name}.mcp`
      link.click()

      this.closeUsageModal()
    },

    async copyInstallCommand() {
      if (!this.selectedService) return

      const command = `# 安装 ${this.selectedService.name}\nnpm install @mcp/${this.selectedService.name.toLowerCase().replace(/\s+/g, '-')}\n\n# 或使用 yarn\nyarn add @mcp/${this.selectedService.name.toLowerCase().replace(/\s+/g, '-')}`

      try {
        await navigator.clipboard.writeText(command)
        console.log('安装命令已复制到剪贴板')
        // You could add a toast notification here
      } catch (error) {
        console.error('复制失败:', error)
      }

      this.closeUsageModal()
    },

    openServiceInBrowser() {
      if (!this.selectedService) return

      // Open service repository or documentation
      const url = `https://github.com/mcp-services/${this.selectedService.name.toLowerCase().replace(/\s+/g, '-')}`
      window.open(url, '_blank')

      this.closeUsageModal()
    },

    viewServiceDetails() {
      if (!this.selectedService) return

      this.closeUsageModal()
      this.$router.push({ name: 'service-detail', params: { id: this.selectedService.id } })
    }
  }
}
</script>

<style lang="scss" scoped>

.mcp-services {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.container {
  width: 100%;
  margin: 0;
  padding: 0 40px;
}

.header-content{
  display: flex;
  justify-content: space-between;
}
/* Header Styles */
.header {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  padding: 10px 0;
  text-align: center;
  color: white;
}
.header-title {
  p {
    padding: 0;
    margin: 0;
  }

  .title {
    font-size: 48px;
    font-weight: 700;
    margin-bottom: 16px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  }

  .subtitle {
    font-size: 19px;
    margin-bottom: 32px;
    opacity: 0.9;
  }
}


.header-actions {
  display: flex;
  gap: 16px;
  justify-content: center;
  flex-wrap: wrap;
}

/* Button Styles */
.btn {
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 8px;

  &-primary {
    background: #4f46e5;
    color: white;

    &:hover {
      background: #4338ca;
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(79, 70, 229, 0.4);
    }
  }

  &-secondary {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.3);

    &:hover {
      background: rgba(255, 255, 255, 0.3);
      transform: translateY(-2px);
    }
  }

  &-sm {
    padding: 8px 16px;
    font-size: 14px;
  }
}

/* Search Section */
.search-section {
  background: white;
  padding: 32px 0;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.search-bar {
  display: flex;
  width: 100%;
  max-width: 800px;
  margin: 0 auto 24px;
  position: relative;
}

.search-input {
  flex: 1;
  padding: 16px 20px;
  border: 2px solid #e5e7eb;
  border-radius: 12px 0 0 12px;
  font-size: 16px;
  outline: none;
  transition: border-color 0.3s ease;

  &:focus {
    border-color: #4f46e5;
  }
}

.search-btn {
  padding: 16px 20px;
  background: #4f46e5;
  color: white;
  border: none;
  border-radius: 0 12px 12px 0;
  cursor: pointer;
  transition: background 0.3s ease;

  &:hover {
    background: #4338ca;
  }
}

.filters {
  display: flex;
  gap: 16px;
  justify-content: center;
  flex-wrap: wrap;
}

.filter-select {
  padding: 12px 16px;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  font-size: 16px;
  background: white;
  cursor: pointer;
  outline: none;
  transition: border-color 0.3s ease;

  &:focus {
    border-color: #4f46e5;
  }
}

/* Services Section */
.services-section {
  padding: 48px 0;
}

.services-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 32px;
  margin-bottom: 32px;
}

.service-card {
  background: white;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  cursor: pointer;
  border: 1px solid #f3f4f6;

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
  }
}

.service-header {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  margin-bottom: 16px;
}

.service-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  overflow: hidden;
  flex-shrink: 0;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

.service-info {
  flex: 1;
}

.service-name {
  font-size: 20px;
  font-weight: 600;
  margin: 0 0 4px 0;
  color: #1f2937;
}

.service-author {
  color: #6b7280;
  margin: 0;
  font-size: 14px;
}

.service-category {
  flex-shrink: 0;
}

.category-tag {
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;

  &.ai {
    background: #dbeafe;
    color: #1e40af;
  }

  &.data {
    background: #d1fae5;
    color: #065f46;
  }

  &.web {
    background: #fef3c7;
    color: #92400e;
  }

  &.tool {
    background: #e0e7ff;
    color: #5b21b6;
  }
}

.service-description {
  margin-bottom: 24px;

  p {
    color: #4b5563;
    line-height: 1.6;
    margin: 0;
  }
}

.service-stats {
  display: flex;
  gap: 24px;
  margin-bottom: 24px;
  padding: 16px;
  background: #f9fafb;
  border-radius: 8px;
}

.stat {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;

  &-icon {
    font-size: 19px;
  }

  &-value {
    font-weight: 600;
    color: #1f2937;
  }

  &-label {
    font-size: 12px;
    color: #6b7280;
  }
}

.service-actions {
  display: flex;
  gap: 12px;
}

/* Loading and Empty States */
.loading {
  text-align: center;
  padding: 48px;
  color: white;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-top: 4px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.empty-state {
  text-align: center;
  padding: 48px;
  color: white;

  h3 {
    margin-bottom: 8px;
  }
}

.empty-icon {
  font-size: 64px;
  margin-bottom: 16px;
}

/* Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 12px;
  width: 90%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #e5e7eb;

  h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: #1f2937;
  }
}

.modal-close {
  background: none;
  border: none;
  font-size: 24px;
  color: #6b7280;
  cursor: pointer;
  padding: 0;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 6px;
  transition: background 0.3s ease;

  &:hover {
    background: #f3f4f6;
  }
}

.modal-body {
  padding: 24px;
}

.usage-options {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-top: 16px;
}

.usage-option {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: left;
  width: 100%;

  &:hover {
    background: #f3f4f6;
    border-color: #4f46e5;
  }

  svg {
    color: #4f46e5;
    flex-shrink: 0;
  }

  span {
    font-weight: 500;
    color: #374151;
  }
}



/* Responsive Design */
@media (max-width: 768px) {
  .container {
    padding: 0 20px;
  }

  .header-title .title {
    font-size: 32px;
  }

  .services-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .service-stats {
    gap: 16px;
  }

  .filters {
    flex-direction: column;
    align-items: center;
  }

  .filter-select {
    width: 100%;
    max-width: 300px;
  }

  .search-bar {
    margin: 0 auto 16px;
  }

  .header-actions {
    flex-direction: column;
    align-items: center;
  }

  .service-actions {
    flex-direction: column;
  }

  .modal-content {
    margin: 20px;
    width: calc(100% - 40px);
  }
}

@media (min-width: 1920px) {
  .services-grid {
    grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  }
}
</style>
