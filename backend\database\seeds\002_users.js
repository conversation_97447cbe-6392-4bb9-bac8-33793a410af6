const bcrypt = require('bcryptjs');

exports.seed = async function (knex) {
  // Deletes ALL existing entries
  await knex('users').del();

  // Hash passwords
  const hashedPassword = await bcrypt.hash('password123', 10);

  // Inserts seed entries
  await knex('users').insert([
    {
      id: 1,
      username: 'admin',
      email: '<EMAIL>',
      password_hash: hashedPassword,
      avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=admin',
      bio: 'MCP Services 平台管理员',
      website: 'https://mcp-services.com',
      location: '北京',
      created_at: new Date(),
      updated_at: new Date()
    },
    {
      id: 2,
      username: 'openai_dev',
      email: '<EMAIL>',
      password_hash: hashedPassword,
      avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=openai',
      bio: 'OpenAI 开发团队',
      website: 'https://openai.com',
      location: '旧金山',
      created_at: new Date(),
      updated_at: new Date()
    },
    {
      id: 3,
      username: 'datacorp',
      email: '<EMAIL>',
      password_hash: hashedPassword,
      avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=datacorp',
      bio: '专注于数据分析和可视化的团队',
      website: 'https://datacorp.com',
      location: '上海',
      created_at: new Date(),
      updated_at: new Date()
    },
    {
      id: 4,
      username: 'webtools',
      email: '<EMAIL>',
      password_hash: hashedPassword,
      avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=webtools',
      bio: 'Web 开发工具专家',
      website: 'https://webtools.dev',
      location: '深圳',
      created_at: new Date(),
      updated_at: new Date()
    }
  ]);
};
