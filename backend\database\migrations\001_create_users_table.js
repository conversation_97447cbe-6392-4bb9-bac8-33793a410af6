exports.up = function (knex) {
  return knex.schema.createTable('users', function (table) {
    table.increments('id').primary();
    table.string('username', 50).notNullable().unique();
    table.string('email', 255).notNullable().unique();
    table.string('password_hash', 255).notNullable();
    table.string('avatar', 500);
    table.text('bio');
    table.string('website', 500);
    table.string('location', 100);
    table.timestamps(true, true); // created_at, updated_at with default values

    // Indexes
    table.index(['username']);
    table.index(['email']);
  });
};

exports.down = function (knex) {
  return knex.schema.dropTable('users');
};
