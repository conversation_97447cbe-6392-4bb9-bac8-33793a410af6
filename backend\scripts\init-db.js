require('dotenv').config();
const mysql = require('mysql2/promise');
const knex = require('knex');
const knexConfig = require('../knexfile');

const environment = process.env.NODE_ENV || 'development';
const config = knexConfig[environment];

async function initDatabase() {
  console.log('🚀 Initializing MCP Services database...');
  
  try {
    // Create database if it doesn't exist
    const connection = await mysql.createConnection({
      host: config.connection.host,
      port: config.connection.port,
      user: config.connection.user,
      password: config.connection.password
    });

    console.log('📡 Connected to MySQL server');

    // Create database
    await connection.execute(`CREATE DATABASE IF NOT EXISTS \`${config.connection.database}\``);
    console.log(`✅ Database '${config.connection.database}' created or already exists`);

    await connection.end();

    // Run migrations
    console.log('🔄 Running migrations...');
    const db = knex(config);
    
    await db.migrate.latest();
    console.log('✅ Migrations completed');

    // Run seeds
    console.log('🌱 Running seeds...');
    await db.seed.run();
    console.log('✅ Seeds completed');

    await db.destroy();
    console.log('🎉 Database initialization completed successfully!');
    
  } catch (error) {
    console.error('❌ Database initialization failed:', error.message);
    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  initDatabase();
}

module.exports = initDatabase;
