<template>
  <div class="publish-container">
    <!-- Top Navigation -->
    <nav class="top-nav">
      <div class="container">
        <div class="nav-content">
          <div class="nav-left">
            <button class="back-btn" @click="goBack">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <path d="m15 18-6-6 6-6"/>
              </svg>
              返回
            </button>
            <div class="breadcrumb">
              <span class="breadcrumb-item">MCP 服务广场</span>
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <path d="m9 18 6-6-6-6"/>
              </svg>
              <span class="breadcrumb-item current">发布服务</span>
            </div>
          </div>
          <div class="nav-right">
            <button class="btn btn-secondary" @click="saveDraft" :disabled="loading">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <path d="M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z"/>
                <polyline points="17,21 17,13 7,13 7,21"/>
                <polyline points="7,3 7,8 15,8"/>
              </svg>
              保存草稿
            </button>
          </div>
        </div>
      </div>
    </nav>

    <!-- Page Header -->
    <div class="page-header">
      <div class="container">
        <div class="header-content">
          <div class="header-icon">
            <svg width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <path d="M12 5v14m-7-7h14"/>
            </svg>
          </div>
          <div class="header-text">
            <h1>发布 MCP 服务</h1>
            <p>分享您的 Model Context Protocol 服务给社区</p>
          </div>
        </div>
      </div>
    </div>

    <div class="publish-content">
      <div class="container">
        <div class="publish-form-container">
          <form @submit.prevent="handleSubmit" class="publish-form">
            <div class="form-section">
              <h2>基本信息</h2>

              <div class="form-group">
                <label for="name">服务名称 *</label>
                <input
                  id="name"
                  v-model="form.name"
                  type="text"
                  required
                  placeholder="请输入服务名称"
                />
              </div>

              <div class="form-group">
                <label for="description">服务描述 *</label>
                <textarea
                  id="description"
                  v-model="form.description"
                  required
                  rows="4"
                  placeholder="请详细描述您的服务功能和用途"
                ></textarea>
              </div>

              <div class="form-row">
                <div class="form-group">
                  <label for="category">分类 *</label>
                  <select id="category" v-model="form.category" required>
                    <option value="">请选择分类</option>
                    <option value="ai">AI 助手</option>
                    <option value="data">数据处理</option>
                    <option value="web">网络服务</option>
                    <option value="tool">工具类</option>
                  </select>
                </div>

                <div class="form-group">
                  <label for="version">版本号 *</label>
                  <input
                    id="version"
                    v-model="form.version"
                    type="text"
                    required
                    placeholder="例如: 1.0.0"
                  />
                </div>
              </div>
            </div>

            <div class="form-section">
              <h2>技术信息</h2>

              <div class="form-group">
                <label for="repository">代码仓库 URL</label>
                <input
                  id="repository"
                  v-model="form.repository"
                  type="url"
                  placeholder="https://github.com/username/repo"
                />
              </div>

              <div class="form-group">
                <label for="documentation">文档链接</label>
                <input
                  id="documentation"
                  v-model="form.documentation"
                  type="url"
                  placeholder="https://docs.example.com"
                />
              </div>

              <div class="form-group">
                <label for="tags">标签</label>
                <input
                  id="tags"
                  v-model="form.tags"
                  type="text"
                  placeholder="用逗号分隔，例如: ai, nlp, chatbot"
                />
                <small>用逗号分隔多个标签</small>
              </div>
            </div>

            <div class="form-section">
              <h2>MCP 配置</h2>

              <div class="form-group">
                <label for="mcpConfig">MCP 配置文件 *</label>
                <textarea
                  id="mcpConfig"
                  v-model="form.mcpConfig"
                  required
                  rows="8"
                  placeholder="请粘贴您的 MCP 配置 JSON"
                ></textarea>
                <small>请提供完整的 MCP 服务配置信息</small>
              </div>
            </div>

            <div v-if="error" class="error-message">
              {{ error }}
            </div>

            <div v-if="success" class="success-message">
              {{ success }}
            </div>

            <div class="form-actions">
              <button type="button" @click="saveDraft" class="btn btn-secondary" :disabled="loading">
                保存草稿
              </button>
              <button type="submit" class="btn btn-primary" :disabled="loading">
                <span v-if="loading" class="spinner"></span>
                {{ loading ? '发布中...' : '发布服务' }}
              </button>
            </div>
          </form>

          <div class="publish-sidebar">
            <div class="sidebar-card">
              <h3>发布指南</h3>
              <ul>
                <li>确保服务名称简洁明了</li>
                <li>提供详细的功能描述</li>
                <li>选择合适的分类标签</li>
                <li>提供完整的 MCP 配置</li>
                <li>添加代码仓库链接</li>
              </ul>
            </div>

            <div class="sidebar-card">
              <h3>MCP 配置示例</h3>
              <pre><code>{
  "name": "my-service",
  "version": "1.0.0",
  "description": "My MCP service",
  "main": "index.js",
  "capabilities": {
    "tools": true,
    "resources": true
  }
}</code></pre>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { useAuthStore } from '@/stores/auth'

export default {
  name: 'PublishView',
  data() {
    return {
      loading: false,
      error: '',
      success: '',
      form: {
        name: '',
        description: '',
        category: '',
        version: '',
        repository: '',
        documentation: '',
        tags: '',
        mcpConfig: ''
      }
    }
  },
  async mounted() {
    const authStore = useAuthStore()
    if (!authStore.isLoggedIn) {
      this.$router.push('/auth')
    }
  },
  methods: {
    async handleSubmit() {
      this.error = ''
      this.success = ''
      this.loading = true

      try {
        // Validate MCP config
        try {
          JSON.parse(this.form.mcpConfig)
        } catch (e) {
          throw new Error('MCP 配置格式不正确，请检查 JSON 格式')
        }

        // Mock API call
        await new Promise(resolve => setTimeout(resolve, 2000))

        // Mock successful publish
        this.success = '服务发布成功！正在审核中，审核通过后将在服务广场显示。'
        this.resetForm()

        // Redirect after success
        setTimeout(() => {
          this.$router.push('/')
        }, 3000)

      } catch (error) {
        this.error = error.message || '发布失败，请重试'
      } finally {
        this.loading = false
      }
    },

    async saveDraft() {
      this.error = ''
      this.success = ''

      try {
        // Mock save draft
        await new Promise(resolve => setTimeout(resolve, 1000))
        this.success = '草稿已保存'

        setTimeout(() => {
          this.success = ''
        }, 3000)
      } catch (error) {
        this.error = '保存失败，请重试'
      }
    },

    resetForm() {
      this.form = {
        name: '',
        description: '',
        category: '',
        version: '',
        repository: '',
        documentation: '',
        tags: '',
        mcpConfig: ''
      }
    },

    goBack() {
      this.$router.go(-1)
    }
  }
}
</script>

<style lang="scss" scoped>
.publish-container {
  min-height: 100vh;
  background: #fafbfc;
}

.container {
  width: 100%;
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 24px;
}

/* Top Navigation */
.top-nav {
  background: white;
  border-bottom: 1px solid #e1e4e8;
  padding: 16px 0;
  position: sticky;
  top: 0;
  z-index: 100;
}

.nav-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 24px;
}

.nav-left {
  display: flex;
  align-items: center;
  gap: 16px;
  flex: 1;
}

.back-btn {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  background: none;
  border: none;
  color: #656d76;
  cursor: pointer;
  font-size: 14px;
  padding: 6px 12px;
  border-radius: 6px;
  transition: all 0.2s ease;

  &:hover {
    background: #f6f8fa;
    color: #24292f;
  }
}

.breadcrumb {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #656d76;

  .breadcrumb-item {
    &.current {
      color: #24292f;
      font-weight: 500;
    }
  }

  svg {
    color: #d0d7de;
  }
}

.nav-right {
  display: flex;
  align-items: center;
  gap: 12px;
}

/* Page Header */
.page-header {
  background: white;
  border-bottom: 1px solid #e1e4e8;
  padding: 32px 0;
}

.header-content {
  display: flex;
  align-items: center;
  gap: 16px;
}

.header-icon {
  width: 48px;
  height: 48px;
  background: #f6f8fa;
  border: 1px solid #d0d7de;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #4f46e5;
}

.header-text {
  h1 {
    font-size: 28px;
    font-weight: 600;
    color: #24292f;
    margin: 0 0 4px 0;
  }

  p {
    font-size: 16px;
    color: #656d76;
    margin: 0;
  }
}

.publish-content {
  padding: 48px 0;
}

.publish-form-container {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 48px;
  align-items: start;
}

.publish-form {
  background: white;
  border-radius: 16px;
  padding: 32px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.form-section {
  margin-bottom: 32px;
  padding-bottom: 32px;
  border-bottom: 1px solid #e5e7eb;

  &:last-of-type {
    border-bottom: none;
    margin-bottom: 0;
  }

  h2 {
    font-size: 20px;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 24px;
  }
}

.form-group {
  margin-bottom: 24px;

  label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #374151;
  }
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.form-group {
  input,
  select,
  textarea {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    font-size: 16px;
    outline: none;
    transition: border-color 0.3s ease;
    box-sizing: border-box;
    font-family: inherit;

    &:focus {
      border-color: #4f46e5;
    }
  }

  small {
    display: block;
    margin-top: 8px;
    color: #6b7280;
    font-size: 14px;
  }
}

.error-message {
  background: #fef2f2;
  color: #dc2626;
  padding: 12px;
  border-radius: 8px;
  margin-bottom: 16px;
  border: 1px solid #fecaca;
}

.success-message {
  background: #f0fdf4;
  color: #16a34a;
  padding: 12px;
  border-radius: 8px;
  margin-bottom: 16px;
  border: 1px solid #bbf7d0;
}

.form-actions {
  display: flex;
  gap: 16px;
  justify-content: flex-end;
  margin-top: 32px;
}

.btn {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 6px 16px;
  border: 1px solid transparent;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  white-space: nowrap;

  &-primary {
    background: #4f46e5;
    color: white;
    border-color: #4f46e5;

    &:hover:not(:disabled) {
      background: #4338ca;
      border-color: #4338ca;
    }
  }

  &-secondary {
    background: #f6f8fa;
    color: #24292f;
    border-color: #d0d7de;

    &:hover:not(:disabled) {
      background: #f3f4f6;
      border-color: #d0d7de;
    }
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
}

.spinner {
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.publish-sidebar {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.sidebar-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);

  h3 {
    font-size: 18px;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 16px;
  }

  ul {
    list-style: none;
    padding: 0;
    margin: 0;
  }

  li {
    padding: 8px 0;
    color: #4b5563;
    border-bottom: 1px solid #f3f4f6;

    &:last-child {
      border-bottom: none;
    }

    &:before {
      content: "✓";
      color: #10b981;
      font-weight: bold;
      margin-right: 8px;
    }
  }

  pre {
    background: #f8fafc;
    border: 1px solid #e5e7eb;
    border-radius: 6px;
    padding: 16px;
    font-size: 14px;
    overflow-x: auto;
    margin: 0;
  }

  code {
    color: #374151;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  }
}

@media (max-width: 768px) {
  .publish-form-container {
    grid-template-columns: 1fr;
    gap: 32px;
  }

  .form-row {
    grid-template-columns: 1fr;
  }

  .form-actions {
    flex-direction: column;
  }
}
</style>
