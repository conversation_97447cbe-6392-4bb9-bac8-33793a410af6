// 简单的API测试脚本
const axios = require('axios');

const API_BASE = 'http://localhost:3001/api/v1';

async function testAPI() {
  console.log('🧪 开始测试 MCP Services API...\n');

  try {
    // 测试健康检查
    console.log('1. 测试健康检查...');
    const healthResponse = await axios.get('http://localhost:3001/health');
    console.log('✅ 健康检查通过:', healthResponse.data.message);

    // 测试获取分类
    console.log('\n2. 测试获取分类...');
    const categoriesResponse = await axios.get(`${API_BASE}/categories`);
    console.log('✅ 分类数量:', categoriesResponse.data.data.categories.length);
    console.log('   分类列表:', categoriesResponse.data.data.categories.map(c => c.name).join(', '));

    // 测试获取服务列表
    console.log('\n3. 测试获取服务列表...');
    const servicesResponse = await axios.get(`${API_BASE}/services`);
    console.log('✅ 服务数量:', servicesResponse.data.data.services.length);
    console.log('   服务列表:', servicesResponse.data.data.services.map(s => s.name).join(', '));

    // 测试获取单个服务
    console.log('\n4. 测试获取服务详情...');
    const serviceResponse = await axios.get(`${API_BASE}/services/1`);
    console.log('✅ 服务详情:', serviceResponse.data.data.service.name);
    console.log('   作者:', serviceResponse.data.data.service.author);
    console.log('   分类:', serviceResponse.data.data.service.category_name);

    // 测试搜索功能
    console.log('\n5. 测试搜索功能...');
    const searchResponse = await axios.get(`${API_BASE}/services?search=AI`);
    console.log('✅ 搜索结果数量:', searchResponse.data.data.services.length);

    // 测试分类筛选
    console.log('\n6. 测试分类筛选...');
    const filterResponse = await axios.get(`${API_BASE}/services?category=ai`);
    console.log('✅ AI分类服务数量:', filterResponse.data.data.services.length);

    console.log('\n🎉 所有API测试通过！');

  } catch (error) {
    console.error('❌ API测试失败:', error.message);
    if (error.response) {
      console.error('   状态码:', error.response.status);
      console.error('   错误信息:', error.response.data);
    }
  }
}

// 运行测试
testAPI();
