exports.seed = async function (knex) {
  // Deletes ALL existing entries
  await knex('services').del();

  // Inserts seed entries
  await knex('services').insert([
    {
      id: 1,
      name: 'AI 代码助手',
      description: '智能代码生成和优化工具，支持多种编程语言。提供智能补全、代码重构建议，以及自动化测试生成等功能。',
      author_id: 2,
      category_id: 'ai',
      version: '2.1.0',
      downloads: 1250,
      rating: 4.8,
      rating_count: 45,
      tags: '["AI", "代码生成", "开发工具", "IDE插件"]',
      icon: 'https://api.dicebear.com/7.x/shapes/svg?seed=ai-code',
      repository: 'https://github.com/openai/ai-code-assistant',
      documentation: 'https://docs.openai.com/ai-code-assistant',
      mcp_config: '{"name": "ai-code-assistant", "version": "2.1.0", "description": "AI-powered code generation and optimization", "capabilities": {"tools": true, "resources": true, "prompts": true}}',
      readme: '# AI 代码助手\n\n智能代码生成和优化工具...',
      status: 'approved'
    },
    {
      id: 2,
      name: '数据分析器',
      description: '强大的数据处理和可视化工具，支持多种数据格式的导入、清洗、分析和可视化。',
      author_id: 3,
      category_id: 'data',
      version: '1.5.3',
      downloads: 890,
      rating: 4.6,
      rating_count: 32,
      tags: '["数据分析", "可视化", "统计", "机器学习"]',
      icon: 'https://api.dicebear.com/7.x/shapes/svg?seed=data-analyzer',
      repository: 'https://github.com/datacorp/data-analyzer',
      documentation: 'https://docs.datacorp.com/analyzer',
      mcp_config: '{"name": "data-analyzer", "version": "1.5.3", "description": "Data processing and visualization tool", "capabilities": {"tools": true, "resources": true}}',
      readme: '# 数据分析器\n\n强大的数据处理工具...',
      status: 'approved'
    },
    {
      id: 3,
      name: 'Web 爬虫',
      description: '高效的网页数据抓取工具，支持动态网页、反爬虫机制绕过、数据清洗和结构化输出。',
      author_id: 4,
      category_id: 'web',
      version: '3.2.1',
      downloads: 2100,
      rating: 4.9,
      rating_count: 67,
      tags: '["爬虫", "数据采集", "自动化", "Web"]',
      icon: 'https://api.dicebear.com/7.x/shapes/svg?seed=web-crawler',
      repository: 'https://github.com/webtools/web-crawler',
      documentation: 'https://docs.webtools.dev/crawler',
      mcp_config: '{"name": "web-crawler", "version": "3.2.1", "description": "Web scraping and data extraction tool", "capabilities": {"tools": true, "resources": true}}',
      readme: '# Web 爬虫\n\n高效的网页数据抓取工具...',
      status: 'approved'
    },
    {
      id: 4,
      name: '文档生成器',
      description: '自动生成项目文档和API文档的工具，支持多种编程语言和框架。',
      author_id: 1,
      category_id: 'tool',
      version: '1.8.0',
      downloads: 567,
      rating: 4.4,
      rating_count: 23,
      tags: '["文档", "自动化", "开发工具", "API"]',
      icon: 'https://api.dicebear.com/7.x/shapes/svg?seed=doc-generator',
      repository: 'https://github.com/admin/doc-generator',
      documentation: 'https://docs.mcp-services.com/doc-generator',
      mcp_config: '{"name": "doc-generator", "version": "1.8.0", "description": "Automatic documentation generator", "capabilities": {"tools": true}}',
      readme: '# 文档生成器\n\n自动生成项目文档...',
      status: 'approved'
    },
    {
      id: 5,
      name: '图像处理器',
      description: '专业的图像处理和编辑工具，支持批量处理、格式转换、滤镜应用等功能。',
      author_id: 4,
      category_id: 'tool',
      version: '2.3.0',
      downloads: 1580,
      rating: 4.7,
      rating_count: 89,
      tags: '["图像处理", "批量操作", "格式转换"]',
      icon: 'https://api.dicebear.com/7.x/shapes/svg?seed=image-processor',
      repository: 'https://github.com/webtools/image-processor',
      documentation: 'https://docs.webtools.dev/image-processor',
      mcp_config: '{"name": "image-processor", "version": "2.3.0", "description": "Professional image processing tool", "capabilities": {"tools": true, "resources": true}}',
      readme: '# 图像处理器\n\n专业的图像处理工具...',
      status: 'approved'
    },
    {
      id: 6,
      name: '智能翻译',
      description: '基于AI的多语言翻译服务，支持100+语言，提供高质量的翻译结果。',
      author_id: 2,
      category_id: 'ai',
      version: '1.2.5',
      downloads: 3200,
      rating: 4.9,
      rating_count: 156,
      tags: '["翻译", "AI", "多语言", "自然语言处理"]',
      icon: 'https://api.dicebear.com/7.x/shapes/svg?seed=translator',
      repository: 'https://github.com/openai/smart-translator',
      documentation: 'https://docs.openai.com/translator',
      mcp_config: '{"name": "smart-translator", "version": "1.2.5", "description": "AI-powered multi-language translator", "capabilities": {"tools": true, "prompts": true}}',
      readme: '# 智能翻译\n\n基于AI的多语言翻译服务...',
      status: 'approved'
    }
  ]);
};
