exports.seed = async function(knex) {
  // Deletes ALL existing entries
  await knex('services').del();
  
  // Inserts seed entries
  await knex('services').insert([
    {
      id: 1,
      name: 'AI 代码助手',
      description: '智能代码生成和优化工具，支持多种编程语言。提供智能补全、代码重构建议，以及自动化测试生成等功能。',
      author_id: 2,
      category_id: 'ai',
      version: '2.1.0',
      downloads: 1250,
      rating: 4.8,
      rating_count: 45,
      tags: JSON.stringify(['AI', '代码生成', '开发工具', 'IDE插件']),
      icon: 'https://api.dicebear.com/7.x/shapes/svg?seed=ai-code',
      repository: 'https://github.com/openai/ai-code-assistant',
      documentation: 'https://docs.openai.com/ai-code-assistant',
      mcp_config: JSON.stringify({
        name: 'ai-code-assistant',
        version: '2.1.0',
        description: 'AI-powered code generation and optimization',
        capabilities: {
          tools: true,
          resources: true,
          prompts: true
        }
      }),
      readme: '# AI 代码助手\n\n智能代码生成和优化工具...',
      status: 'approved'
    },
    {
      id: 2,
      name: '数据分析器',
      description: '强大的数据处理和可视化工具，支持多种数据格式的导入、清洗、分析和可视化。',
      author_id: 3,
      category_id: 'data',
      version: '1.5.3',
      downloads: 890,
      rating: 4.6,
      rating_count: 32,
      tags: JSON.stringify(['数据分析', '可视化', '统计', '机器学习']),
      icon: 'https://api.dicebear.com/7.x/shapes/svg?seed=data-analyzer',
      repository: 'https://github.com/datacorp/data-analyzer',
      documentation: 'https://docs.datacorp.com/analyzer',
      mcp_config: JSON.stringify({
        name: 'data-analyzer',
        version: '1.5.3',
        description: 'Data processing and visualization tool',
        capabilities: {
          tools: true,
          resources: true
        }
      }),
      readme: '# 数据分析器\n\n强大的数据处理工具...',
      status: 'approved'
    }
  ]);
};
