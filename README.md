# MCP 服务发布网站

一个基于 Vue.js 的 Model Context Protocol (MCP) 服务发布和分享平台。

## 项目概述

本项目是一个仿照 ModelScope MCP 广场的网站，允许用户浏览、使用和发布 MCP 服务。主要功能包括：

- 🔍 **服务浏览**: 无需登录即可浏览和搜索 MCP 服务
- 🔐 **用户认证**: 完整的登录/注册系统
- 📝 **服务发布**: 登录用户可以发布自己的 MCP 服务
- 🏷️ **分类管理**: 支持服务分类和标签
- 📱 **响应式设计**: 针对 1920x1080 分辨率优化，支持移动端

## 技术栈

- **前端框架**: Vue 3
- **构建工具**: Vite
- **路由管理**: Vue Router
- **状态管理**: Pinia
- **样式**: CSS3 (Scoped CSS)
- **开发语言**: JavaScript

## 项目结构

```
mcp-website/
├── src/
│   ├── components/          # 可复用组件
│   ├── views/              # 页面组件
│   │   ├── HomeView.vue    # 主页 - MCP 服务列表
│   │   ├── AuthView.vue    # 登录/注册页面
│   │   └── PublishView.vue # 服务发布页面
│   ├── stores/             # Pinia 状态管理
│   │   └── auth.js         # 认证状态管理
│   ├── router/             # 路由配置
│   │   └── index.js        # 路由定义
│   ├── assets/             # 静态资源
│   ├── App.vue             # 根组件
│   └── main.js             # 应用入口
├── public/                 # 公共资源
├── API_DOCUMENTATION.md    # API 接口文档
├── package.json            # 项目配置
└── vite.config.js         # Vite 配置
```

## 主要功能

### 1. 服务浏览 (HomeView)
- 展示所有可用的 MCP 服务
- 支持按分类筛选 (AI助手、数据处理、网络服务、工具类)
- 支持关键词搜索
- 支持多种排序方式 (最新、最受欢迎、名称)
- 显示服务统计信息 (下载量、评分、更新时间)

### 2. 用户认证 (AuthView)
- 用户注册功能
- 用户登录功能
- JWT Token 认证
- 本地存储状态持久化

### 3. 服务发布 (PublishView)
- 需要登录才能访问
- 完整的服务信息表单
- MCP 配置文件上传
- 草稿保存功能
- 发布指南和示例

### 4. 状态管理
- 用户认证状态管理
- 自动检查本地存储的认证信息
- 路由守卫保护需要认证的页面

## 安装和运行

### 前置要求
- Node.js 16+
- npm 或 yarn

### 安装依赖
```bash
cd mcp-website
npm install
```

### 开发模式
```bash
npm run dev
```

### 构建生产版本
```bash
npm run build
```

### 预览生产版本
```bash
npm run preview
```

## 设计特点

### 响应式设计
- 针对 1920x1080 分辨率优化
- 支持移动端和平板设备
- 使用 CSS Grid 和 Flexbox 布局

### 用户体验
- 现代化的渐变背景设计
- 流畅的动画和过渡效果
- 直观的用户界面
- 加载状态和错误处理

### 安全性
- JWT Token 认证
- 路由守卫保护
- 表单验证
- XSS 防护

## API 接口

详细的 API 接口文档请参考 [API_DOCUMENTATION.md](./API_DOCUMENTATION.md)

主要接口包括：
- 用户认证 (`/auth/*`)
- 服务管理 (`/services/*`)
- 分类管理 (`/categories/*`)
- 统计功能 (`/services/{id}/download`, `/services/{id}/rating`)

## 开发说明

### 模拟数据
当前版本使用模拟数据进行演示，包括：
- 示例 MCP 服务数据
- 模拟的用户认证 (用户名: admin, 密码: password)
- 模拟的 API 调用延迟

### 后端集成
要集成真实的后端 API，需要：
1. 更新 `stores/auth.js` 中的 API 调用
2. 更新 `views/HomeView.vue` 中的服务数据获取
3. 更新 `views/PublishView.vue` 中的服务发布逻辑
4. 配置正确的 API 基础 URL

### 环境配置
可以通过环境变量配置：
```bash
VITE_API_BASE_URL=https://api.mcp-services.com/v1
VITE_APP_TITLE=MCP 服务广场
```

## 部署

### 静态部署
构建后的文件可以部署到任何静态文件服务器：
```bash
npm run build
# 将 dist/ 目录部署到服务器
```

### Docker 部署
```dockerfile
FROM nginx:alpine
COPY dist/ /usr/share/nginx/html/
EXPOSE 80
```

## 模拟数据说明

### 登录测试账户
- 用户名: `admin`
- 密码: `password`

### 示例服务数据
项目包含4个示例MCP服务：
1. AI 代码助手 - AI分类
2. 数据分析器 - 数据处理分类
3. Web 爬虫 - 网络服务分类
4. 文档生成器 - 工具类分类

## 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

---

**注意**: 这是一个演示项目，当前使用模拟数据。在生产环境中使用前，请确保集成真实的后端 API 并进行充分的安全测试。
