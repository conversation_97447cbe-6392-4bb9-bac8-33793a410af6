# MCP 服务发布网站 API 文档

## 概述

本文档描述了 MCP 服务发布网站所需的后端 API 接口。所有 API 都使用 RESTful 设计，返回 JSON 格式数据。

## 基础信息

- **Base URL**: `https://api.mcp-services.com/v1`
- **认证方式**: JWT <PERSON> (Bearer Token)
- **内容类型**: `application/json`

## 认证相关 API

### 1. 用户注册

**POST** `/auth/register`

**请求体**:
```json
{
  "username": "string",
  "email": "string",
  "password": "string"
}
```

**响应**:
```json
{
  "success": true,
  "data": {
    "user": {
      "id": "number",
      "username": "string",
      "email": "string",
      "avatar": "string",
      "createdAt": "string"
    },
    "token": "string"
  }
}
```

### 2. 用户登录

**POST** `/auth/login`

**请求体**:
```json
{
  "username": "string",
  "password": "string"
}
```

**响应**:
```json
{
  "success": true,
  "data": {
    "user": {
      "id": "number",
      "username": "string",
      "email": "string",
      "avatar": "string"
    },
    "token": "string"
  }
}
```

### 3. 刷新令牌

**POST** `/auth/refresh`

**Headers**: `Authorization: Bearer <token>`

**响应**:
```json
{
  "success": true,
  "data": {
    "token": "string"
  }
}
```

### 4. 用户登出

**POST** `/auth/logout`

**Headers**: `Authorization: Bearer <token>`

**响应**:
```json
{
  "success": true,
  "message": "Successfully logged out"
}
```

## MCP 服务相关 API

### 1. 获取服务列表

**GET** `/services`

**查询参数**:
- `page`: 页码 (默认: 1)
- `limit`: 每页数量 (默认: 20)
- `category`: 分类筛选 (可选)
- `search`: 搜索关键词 (可选)
- `sort`: 排序方式 (latest, popular, name)

**响应**:
```json
{
  "success": true,
  "data": {
    "services": [
      {
        "id": "number",
        "name": "string",
        "description": "string",
        "author": {
          "id": "number",
          "username": "string",
          "avatar": "string"
        },
        "category": "string",
        "version": "string",
        "downloads": "number",
        "rating": "number",
        "tags": ["string"],
        "icon": "string",
        "repository": "string",
        "documentation": "string",
        "createdAt": "string",
        "updatedAt": "string",
        "status": "string"
      }
    ],
    "pagination": {
      "page": "number",
      "limit": "number",
      "total": "number",
      "totalPages": "number"
    }
  }
}
```

### 2. 获取服务详情

**GET** `/services/{id}`

**响应**:
```json
{
  "success": true,
  "data": {
    "id": "number",
    "name": "string",
    "description": "string",
    "author": {
      "id": "number",
      "username": "string",
      "avatar": "string"
    },
    "category": "string",
    "version": "string",
    "downloads": "number",
    "rating": "number",
    "tags": ["string"],
    "icon": "string",
    "repository": "string",
    "documentation": "string",
    "mcpConfig": "object",
    "readme": "string",
    "changelog": "string",
    "createdAt": "string",
    "updatedAt": "string",
    "status": "string"
  }
}
```

### 3. 发布新服务

**POST** `/services`

**Headers**: `Authorization: Bearer <token>`

**请求体**:
```json
{
  "name": "string",
  "description": "string",
  "category": "string",
  "version": "string",
  "tags": ["string"],
  "repository": "string",
  "documentation": "string",
  "mcpConfig": "object",
  "icon": "string"
}
```

**响应**:
```json
{
  "success": true,
  "data": {
    "id": "number",
    "name": "string",
    "status": "pending",
    "message": "Service submitted for review"
  }
}
```

### 4. 更新服务

**PUT** `/services/{id}`

**Headers**: `Authorization: Bearer <token>`

**请求体**: (同发布新服务)

**响应**:
```json
{
  "success": true,
  "data": {
    "id": "number",
    "message": "Service updated successfully"
  }
}
```

### 5. 删除服务

**DELETE** `/services/{id}`

**Headers**: `Authorization: Bearer <token>`

**响应**:
```json
{
  "success": true,
  "message": "Service deleted successfully"
}
```

### 6. 获取用户发布的服务

**GET** `/users/me/services`

**Headers**: `Authorization: Bearer <token>`

**查询参数**:
- `page`: 页码 (默认: 1)
- `limit`: 每页数量 (默认: 20)
- `status`: 状态筛选 (pending, approved, rejected)

**响应**: (同获取服务列表)

## 服务统计 API

### 1. 增加下载次数

**POST** `/services/{id}/download`

**响应**:
```json
{
  "success": true,
  "data": {
    "downloads": "number"
  }
}
```

### 2. 服务评分

**POST** `/services/{id}/rating`

**Headers**: `Authorization: Bearer <token>`

**请求体**:
```json
{
  "rating": "number"
}
```

**响应**:
```json
{
  "success": true,
  "data": {
    "rating": "number",
    "totalRatings": "number"
  }
}
```

## 分类管理 API

### 1. 获取所有分类

**GET** `/categories`

**响应**:
```json
{
  "success": true,
  "data": [
    {
      "id": "string",
      "name": "string",
      "description": "string",
      "icon": "string",
      "serviceCount": "number"
    }
  ]
}
```

## 错误响应格式

所有错误响应都遵循以下格式：

```json
{
  "success": false,
  "error": {
    "code": "string",
    "message": "string",
    "details": "object"
  }
}
```

## 状态码说明

- `200`: 成功
- `201`: 创建成功
- `400`: 请求参数错误
- `401`: 未认证
- `403`: 权限不足
- `404`: 资源不存在
- `409`: 资源冲突
- `422`: 数据验证失败
- `500`: 服务器内部错误

## 数据模型

### User 模型
```json
{
  "id": "number",
  "username": "string",
  "email": "string",
  "avatar": "string",
  "bio": "string",
  "website": "string",
  "location": "string",
  "createdAt": "string",
  "updatedAt": "string"
}
```

### Service 模型
```json
{
  "id": "number",
  "name": "string",
  "description": "string",
  "authorId": "number",
  "category": "string",
  "version": "string",
  "downloads": "number",
  "rating": "number",
  "ratingCount": "number",
  "tags": ["string"],
  "icon": "string",
  "repository": "string",
  "documentation": "string",
  "mcpConfig": "object",
  "readme": "string",
  "changelog": "string",
  "status": "string",
  "createdAt": "string",
  "updatedAt": "string"
}
```

### MCP Config 示例
```json
{
  "name": "my-mcp-service",
  "version": "1.0.0",
  "description": "My MCP service description",
  "main": "index.js",
  "capabilities": {
    "tools": true,
    "resources": true,
    "prompts": false
  },
  "tools": [
    {
      "name": "example_tool",
      "description": "An example tool",
      "inputSchema": {
        "type": "object",
        "properties": {
          "query": {
            "type": "string",
            "description": "The query to process"
          }
        },
        "required": ["query"]
      }
    }
  ],
  "resources": [
    {
      "uri": "file://example.txt",
      "name": "Example Resource",
      "description": "An example resource"
    }
  ]
}
```
